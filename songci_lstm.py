#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宋词LSTM文本生成系统
基于宋词合集.csv的完整LSTM文本生成解决方案
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import re
import pickle
import os
import time
from collections import Counter
from typing import List, Tuple, Optional

# ==================== 数据预处理 ====================

class SongCiDataset(Dataset):
    """宋词数据集"""
    
    def __init__(self, texts: List[str], char_to_idx: dict, seq_length: int = 50):
        self.texts = texts
        self.char_to_idx = char_to_idx
        self.seq_length = seq_length
        self.sequences = self._create_sequences()
    
    def _create_sequences(self):
        """创建训练序列"""
        sequences = []
        
        # 合并所有文本
        full_text = ''.join(self.texts)
        
        # 转换为索引
        indices = [self.char_to_idx.get(char, 0) for char in full_text]
        
        # 创建滑动窗口序列
        for i in range(len(indices) - self.seq_length):
            input_seq = indices[i:i + self.seq_length]
            target_seq = indices[i + 1:i + self.seq_length + 1]
            sequences.append((input_seq, target_seq))
        
        return sequences
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        input_seq, target_seq = self.sequences[idx]
        return torch.tensor(input_seq, dtype=torch.long), torch.tensor(target_seq, dtype=torch.long)

def load_and_preprocess_data(csv_file: str, sample_size: Optional[int] = None):
    """加载和预处理宋词数据"""
    print(f"加载数据: {csv_file}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file, encoding='utf-8')
    print(f"总共 {len(df)} 首词")
    
    # 采样（如果指定）
    if sample_size and sample_size < len(df):
        df = df.sample(n=sample_size, random_state=42)
        print(f"采样 {sample_size} 首词")
    
    # 提取和清理文本
    texts = []
    for _, row in df.iterrows():
        content = row['内容']
        if pd.notna(content) and len(content.strip()) > 0:
            # 清理文本：将|替换为句号，移除多余空格
            cleaned = content.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            cleaned = re.sub(r'\s+', '', cleaned)
            if len(cleaned) > 10:  # 过滤太短的文本
                texts.append(cleaned)
    
    print(f"有效文本数量: {len(texts)}")
    
    # 构建字符词汇表
    all_chars = ''.join(texts)
    char_counts = Counter(all_chars)
    
    # 创建字符到索引的映射
    chars = sorted(char_counts.keys())
    char_to_idx = {char: idx for idx, char in enumerate(chars)}
    idx_to_char = {idx: char for char, idx in char_to_idx.items()}
    
    vocab_size = len(char_to_idx)
    print(f"词汇表大小: {vocab_size}")
    print(f"最常见字符: {char_counts.most_common(10)}")
    
    return texts, char_to_idx, idx_to_char, vocab_size

# ==================== LSTM模型 ====================

class SongCiLSTM(nn.Module):
    """宋词LSTM生成模型"""
    
    def __init__(self, vocab_size: int, embedding_dim: int = 256, 
                 hidden_dim: int = 512, num_layers: int = 2, dropout: float = 0.3):
        super(SongCiLSTM, self).__init__()
        
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 嵌入层
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=embedding_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, vocab_size)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        # 嵌入层
        nn.init.uniform_(self.embedding.weight, -0.1, 0.1)
        
        # LSTM权重
        for name, param in self.lstm.named_parameters():
            if 'weight_ih' in name:
                nn.init.xavier_uniform_(param.data)
            elif 'weight_hh' in name:
                nn.init.orthogonal_(param.data)
            elif 'bias' in name:
                param.data.fill_(0)
                # 设置forget gate bias为1
                n = param.size(0)
                param.data[(n//4):(n//2)].fill_(1)
        
        # 输出层
        nn.init.xavier_uniform_(self.output_layer.weight)
        nn.init.zeros_(self.output_layer.bias)
    
    def forward(self, x, hidden=None):
        """前向传播"""
        # 嵌入
        embedded = self.embedding(x)  # [batch_size, seq_len, embedding_dim]
        
        # LSTM
        lstm_out, hidden = self.lstm(embedded, hidden)  # [batch_size, seq_len, hidden_dim]
        
        # Dropout
        lstm_out = self.dropout(lstm_out)
        
        # 输出
        output = self.output_layer(lstm_out)  # [batch_size, seq_len, vocab_size]
        
        return output, hidden
    
    def init_hidden(self, batch_size, device):
        """初始化隐藏状态"""
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim, device=device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim, device=device)
        return (h0, c0)

# ==================== 训练函数 ====================

def train_model(model, train_loader, char_to_idx, idx_to_char, num_epochs=20, learning_rate=0.001, device='cpu'):
    """训练模型"""
    print(f"开始训练，设备: {device}")

    # 优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.CrossEntropyLoss()
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=3, factor=0.5, verbose=True)

    model.to(device)
    model.train()

    train_losses = []

    for epoch in range(num_epochs):
        epoch_loss = 0
        num_batches = len(train_loader)

        for batch_idx, (inputs, targets) in enumerate(train_loader):
            inputs, targets = inputs.to(device), targets.to(device)

            # 初始化隐藏状态
            hidden = model.init_hidden(inputs.size(0), device)

            # 前向传播
            outputs, hidden = model(inputs, hidden)

            # 计算损失
            loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))

            # 反向传播
            optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)

            # 更新参数
            optimizer.step()

            epoch_loss += loss.item()

            # 打印进度
            if batch_idx % 50 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}')

        avg_loss = epoch_loss / num_batches
        train_losses.append(avg_loss)

        # 更新学习率
        scheduler.step(avg_loss)

        print(f'Epoch {epoch+1}/{num_epochs} 完成, 平均损失: {avg_loss:.4f}')

        # 每2个epoch生成样本
        if (epoch + 1) % 2 == 0:
            generate_sample(model, char_to_idx, idx_to_char, device, start_text="春")

    return train_losses

# ==================== 文本生成 ====================

def generate_text(model, char_to_idx, idx_to_char, device, start_text="",
                 max_length=100, temperature=1.0, top_k=0, top_p=0.0,
                 repetition_penalty=1.1, max_repeat=2):
    """生成文本（改进版，防止重复）"""
    model.eval()

    with torch.no_grad():
        # 处理起始文本
        if start_text:
            input_chars = list(start_text)
        else:
            input_chars = [list(char_to_idx.keys())[0]]  # 使用第一个字符作为起始

        # 转换为索引
        input_indices = [char_to_idx.get(char, 0) for char in input_chars]

        # 初始化隐藏状态
        hidden = model.init_hidden(1, device)

        generated_chars = input_chars.copy()
        generated_indices = input_indices.copy()

        # 如果有起始文本，先处理起始文本
        if len(input_indices) > 1:
            input_tensor = torch.tensor([input_indices[:-1]], device=device)
            _, hidden = model(input_tensor, hidden)
            current_input = input_indices[-1]
        else:
            current_input = input_indices[0]

        # 生成文本
        for step in range(max_length):
            input_tensor = torch.tensor([[current_input]], device=device)
            output, hidden = model(input_tensor, hidden)

            # 获取logits
            logits = output[0, -1].clone()

            # 应用重复惩罚
            if repetition_penalty != 1.0:
                # 对已生成的字符应用惩罚
                for idx in set(generated_indices):
                    logits[idx] /= repetition_penalty

            # 防止连续重复相同字符
            if len(generated_indices) >= max_repeat:
                last_chars = generated_indices[-max_repeat:]
                if len(set(last_chars)) == 1:  # 如果最后几个字符都相同
                    repeated_char_idx = last_chars[0]
                    logits[repeated_char_idx] = float('-inf')  # 禁止再次生成

            # 应用温度
            if temperature != 1.0:
                logits = logits / temperature

            # Top-K采样
            if top_k > 0:
                top_k_logits, top_k_indices = torch.topk(logits, min(top_k, logits.size(-1)))
                logits = torch.full_like(logits, float('-inf'))
                logits.scatter_(0, top_k_indices, top_k_logits)

            # Top-P采样
            if top_p > 0.0:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                # 移除累积概率超过top_p的token
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                sorted_indices_to_remove[0] = 0

                indices_to_remove = sorted_indices[sorted_indices_to_remove]
                logits[indices_to_remove] = float('-inf')

            # 计算概率分布
            probs = F.softmax(logits, dim=-1)

            # 采样下一个字符
            next_char_idx = torch.multinomial(probs, 1).item()
            next_char = idx_to_char.get(next_char_idx, '')

            generated_chars.append(next_char)
            generated_indices.append(next_char_idx)
            current_input = next_char_idx

            # 如果生成了句号，可能结束
            if next_char == '。' and len(generated_chars) > 20:
                break

        return ''.join(generated_chars)

def generate_sample(model, char_to_idx, idx_to_char, device, start_text="春"):
    """生成样本文本用于训练时查看"""
    sample = generate_text(model, char_to_idx, idx_to_char, device, start_text, max_length=50)
    print(f"生成样本: {sample}")

# ==================== 主函数 ====================

def main():
    """主函数"""
    # 参数设置
    CSV_FILE = "宋词合集.csv"
    SAMPLE_SIZE = 200  # 设置为None使用全部数据，或设置数字如100进行快速测试
    SEQ_LENGTH = 50
    BATCH_SIZE = 32
    EMBEDDING_DIM = 256
    HIDDEN_DIM = 512
    NUM_LAYERS = 2
    DROPOUT = 0.3
    NUM_EPOCHS = 25  # 快速测试用较少epoch
    LEARNING_RATE = 0.001

    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 1. 数据预处理
    print("\n=== 数据预处理 ===")
    texts, char_to_idx, idx_to_char, vocab_size = load_and_preprocess_data(CSV_FILE, SAMPLE_SIZE)

    # 保存词汇表
    with open('char_mappings.pkl', 'wb') as f:
        pickle.dump({'char_to_idx': char_to_idx, 'idx_to_char': idx_to_char}, f)

    # 创建数据集和数据加载器
    dataset = SongCiDataset(texts, char_to_idx, SEQ_LENGTH)
    train_loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)

    print(f"训练序列数量: {len(dataset)}")

    # 2. 创建模型
    print("\n=== 创建模型 ===")
    model = SongCiLSTM(
        vocab_size=vocab_size,
        embedding_dim=EMBEDDING_DIM,
        hidden_dim=HIDDEN_DIM,
        num_layers=NUM_LAYERS,
        dropout=DROPOUT
    )

    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {total_params:,}")

    # 3. 训练模型
    print("\n=== 训练模型 ===")
    train_losses = train_model(
        model, train_loader, char_to_idx, idx_to_char,
        num_epochs=NUM_EPOCHS,
        learning_rate=LEARNING_RATE,
        device=device
    )

    # 4. 保存模型
    print("\n=== 保存模型 ===")
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'embedding_dim': EMBEDDING_DIM,
        'hidden_dim': HIDDEN_DIM,
        'num_layers': NUM_LAYERS,
        'dropout': DROPOUT,
        'train_losses': train_losses
    }, 'songci_lstm_model.pth')
    print("模型已保存为 songci_lstm_model.pth")

    # 5. 生成文本示例
    print("\n=== 生成文本示例 ===")
    model.eval()

    # 生成多个样本
    start_texts = ["春", "花", "月"]
    temperatures = [0.8, 1.0]

    for start_text in start_texts:
        for temp in temperatures:
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text, max_length=60, temperature=temp
            )
            print(f"起始: '{start_text}', 温度: {temp} -> {generated}")
        print()

if __name__ == "__main__":
    main()
