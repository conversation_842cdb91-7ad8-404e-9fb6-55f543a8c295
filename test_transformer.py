#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练完成的Transformer模型
"""

import torch
import torch.nn.functional as F
import pickle
import os
from songci_transformer import SongCiTransformer

def load_model_and_vocab():
    """加载模型和词汇表"""
    model_path = 'songci_transformer_model.pth'
    vocab_path = 'transformer_char_mappings.pkl'
    
    print("=== 检查文件 ===")
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None, None, None
    
    if not os.path.exists(vocab_path):
        print(f"❌ 词汇表文件不存在: {vocab_path}")
        return None, None, None
    
    print(f"✅ 模型文件存在: {model_path}")
    print(f"✅ 词汇表文件存在: {vocab_path}")
    
    try:
        # 加载词汇表
        with open(vocab_path, 'rb') as f:
            mappings = pickle.load(f)
            char_to_idx = mappings['char_to_idx']
            idx_to_char = mappings['idx_to_char']
        
        print(f"✅ 词汇表加载成功，大小: {len(char_to_idx)}")
        
        # 加载模型
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"✅ 模型检查点加载成功")
        
        # 打印模型信息
        print("\n=== 模型信息 ===")
        print(f"词汇表大小: {checkpoint['vocab_size']}")
        print(f"模型维度: {checkpoint['d_model']}")
        print(f"注意力头数: {checkpoint['nhead']}")
        print(f"Transformer层数: {checkpoint['num_layers']}")
        print(f"前馈网络维度: {checkpoint['dim_feedforward']}")
        print(f"Dropout率: {checkpoint['dropout']}")
        
        # 创建模型（使用训练时的参数）
        model = SongCiTransformer(
            vocab_size=checkpoint['vocab_size'],
            d_model=checkpoint['d_model'],
            nhead=checkpoint['nhead'],
            num_layers=checkpoint['num_layers'],
            dim_feedforward=checkpoint['dim_feedforward'],
            dropout=checkpoint['dropout'],
            max_len=256  # 根据位置编码的形状设置
        )
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        print("✅ 模型加载成功")
        
        return model, char_to_idx, idx_to_char
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None, None, None

def test_model_forward(model, char_to_idx, device):
    """测试模型前向传播"""
    print("\n=== 测试模型前向传播 ===")
    
    try:
        # 创建测试输入
        test_text = "春风又绿江南岸"
        test_indices = [char_to_idx.get(char, 0) for char in test_text]
        test_input = torch.tensor([test_indices], device=device)
        
        print(f"测试文本: '{test_text}'")
        print(f"输入形状: {test_input.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model(test_input)
        
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        
        # 检查输出是否合理
        if output.shape[0] == 1 and output.shape[1] == len(test_indices) and output.shape[2] == len(char_to_idx):
            print("✅ 前向传播测试通过")
            return True
        else:
            print("❌ 输出形状不正确")
            return False
            
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        return False

def generate_text(model, char_to_idx, idx_to_char, device, start_text="春", 
                 max_length=50, temperature=1.0):
    """生成文本"""
    model.eval()
    
    with torch.no_grad():
        # 处理起始文本
        input_chars = list(start_text)
        input_indices = [char_to_idx.get(char, 0) for char in input_chars]
        generated_indices = input_indices.copy()
        
        # 生成文本
        for _ in range(max_length):
            # 准备输入序列（限制长度）
            if len(generated_indices) > 100:
                current_indices = generated_indices[-100:]
            else:
                current_indices = generated_indices
            
            current_seq = torch.tensor([current_indices], device=device)
            
            # 前向传播
            outputs = model(current_seq)
            
            # 获取最后一个位置的logits
            logits = outputs[0, -1, :] / temperature
            probs = F.softmax(logits, dim=-1)
            
            # 采样下一个字符
            next_char_idx = torch.multinomial(probs, 1).item()
            next_char = idx_to_char.get(next_char_idx, '')
            
            generated_indices.append(next_char_idx)
            
            # 如果生成了句号，可能结束
            if next_char == '。' and len(generated_indices) > 10:
                break
        
        # 转换为文本
        generated_chars = [idx_to_char.get(idx, '') for idx in generated_indices]
        return ''.join(generated_chars)

def test_text_generation(model, char_to_idx, idx_to_char, device):
    """测试文本生成"""
    print("\n=== 测试文本生成 ===")
    
    try:
        # 测试不同的起始文本和温度
        test_cases = [
            ("春", 0.8),
            ("花", 1.0),
            ("月", 1.2),
            ("风", 0.6),
            ("雨", 1.0)
        ]
        
        for start_text, temperature in test_cases:
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text, max_length=60, temperature=temperature
            )
            print(f"起始: '{start_text}' (温度: {temperature}) -> {generated}")
        
        print("✅ 文本生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文本生成测试失败: {e}")
        return False

def test_vocabulary(char_to_idx, idx_to_char):
    """测试词汇表"""
    print("\n=== 测试词汇表 ===")
    
    try:
        vocab_size = len(char_to_idx)
        print(f"词汇表大小: {vocab_size}")
        
        # 检查常见字符
        common_chars = ['春', '花', '月', '风', '雨', '。', '，']
        missing_chars = []
        
        for char in common_chars:
            if char not in char_to_idx:
                missing_chars.append(char)
        
        if missing_chars:
            print(f"⚠️  缺少常见字符: {missing_chars}")
        else:
            print("✅ 常见字符都存在")
        
        # 显示最常见的字符
        print("词汇表示例:")
        for i, (char, idx) in enumerate(list(char_to_idx.items())[:10]):
            print(f"  '{char}' -> {idx}")
        
        # 检查映射一致性
        inconsistent = 0
        for char, idx in char_to_idx.items():
            if idx_to_char.get(idx) != char:
                inconsistent += 1
        
        if inconsistent == 0:
            print("✅ 字符映射一致性检查通过")
        else:
            print(f"❌ 发现 {inconsistent} 个不一致的映射")
        
        return True
        
    except Exception as e:
        print(f"❌ 词汇表测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Transformer模型")
    print("=" * 60)
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型和词汇表
    model, char_to_idx, idx_to_char = load_model_and_vocab()
    
    if model is None:
        print("❌ 模型加载失败，测试终止")
        return
    
    model.to(device)
    
    # 运行测试
    tests_passed = 0
    total_tests = 4
    
    # 1. 测试词汇表
    if test_vocabulary(char_to_idx, idx_to_char):
        tests_passed += 1
    
    # 2. 测试模型前向传播
    if test_model_forward(model, char_to_idx, device):
        tests_passed += 1
    
    # 3. 测试文本生成
    if test_text_generation(model, char_to_idx, idx_to_char, device):
        tests_passed += 1
    
    # 4. 交互式测试
    print("\n=== 交互式测试 ===")
    print("输入起始文字测试生成（输入 'quit' 退出）:")
    
    interactive_passed = True
    try:
        while True:
            user_input = input("\n请输入起始文字: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            if not user_input:
                continue
            
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=user_input, max_length=80, temperature=1.0
            )
            
            print(f"生成结果: {generated}")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"交互式测试出错: {e}")
        interactive_passed = False
    
    if interactive_passed:
        tests_passed += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"🎯 测试完成: {tests_passed}/{total_tests} 项测试通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！Transformer模型运行正常。")
        print("\n📝 使用建议:")
        print("  python generate_transformer.py --start '春' --temperature 0.8")
        print("  python generate_transformer.py --interactive")
    else:
        print("⚠️  部分测试失败，请检查模型或数据。")

if __name__ == "__main__":
    main()
