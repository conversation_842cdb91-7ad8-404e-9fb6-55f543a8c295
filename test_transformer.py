#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Transformer宋词生成效果
"""

import torch
import pickle
from songci_transformer_complete import TransformerModel, generate_text

def test_generation():
    """测试生成功能"""
    print("🚀 测试Transformer宋词生成")
    print("=" * 50)
    
    try:
        # 加载词汇表
        with open('transformer_char_mappings.pkl', 'rb') as f:
            mappings = pickle.load(f)
            char_to_idx = mappings['char_to_idx']
            idx_to_char = mappings['idx_to_char']
        
        print(f"✅ 词汇表加载成功，大小: {len(char_to_idx)}")
        
        # 加载模型
        checkpoint = torch.load('songci_transformer_model.pth', map_location='cpu')
        
        model = TransformerModel(
            ntoken=checkpoint['vocab_size'],
            ninp=checkpoint['ninp'],
            nhead=checkpoint['nhead'],
            nhid=checkpoint['nhid'],
            nlayers=checkpoint['nlayers'],
            dropout=checkpoint['dropout']
        )
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✅ Transformer模型加载成功")
        print(f"模型参数: vocab_size={checkpoint['vocab_size']}, ninp={checkpoint['ninp']}")
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        print(f"使用设备: {device}")
        
        # 测试不同主题的生成
        print("\n=== 生成测试 ===")
        
        test_cases = [
            ("春", "春天主题"),
            ("花", "花朵主题"),
            ("月", "月亮主题"),
            ("风", "风的主题"),
            ("雨", "雨的主题"),
            ("山", "山水主题"),
            ("水", "水的主题"),
            ("情", "情感主题"),
            ("梦", "梦境主题"),
            ("酒", "酒的主题")
        ]
        
        for start_text, description in test_cases:
            print(f"\n{description} (起始: '{start_text}'):")
            print("-" * 40)
            
            # 生成3个不同温度的版本
            temperatures = [0.6, 0.8, 1.0]
            
            for temp in temperatures:
                generated = generate_text(
                    model, char_to_idx, idx_to_char, device,
                    start_text=start_text,
                    max_length=60,
                    temperature=temp
                )
                print(f"温度{temp}: {generated}")
        
        # 测试高级采样
        print("\n=== 高级采样测试 ===")
        
        advanced_tests = [
            ("春", {"temperature": 0.8, "top_k": 20}, "Top-K采样"),
            ("花", {"temperature": 0.8, "top_p": 0.9}, "Top-P采样"),
            ("月", {"temperature": 0.8, "top_k": 30, "top_p": 0.9}, "组合采样")
        ]
        
        for start_text, params, method in advanced_tests:
            print(f"\n{method} (起始: '{start_text}'):")
            print("-" * 30)
            
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text,
                max_length=80,
                **params
            )
            print(generated)
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_generation()
