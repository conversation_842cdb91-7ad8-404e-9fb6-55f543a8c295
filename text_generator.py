#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宋词文本生成器
"""

import torch
import torch.nn.functional as F
import numpy as np
import argparse
import os
from typing import List, Optional

from data_preprocessing import PoetryVocabulary
from lstm_model import create_model

class SongCiGenerator:
    """宋词生成器"""
    
    def __init__(self, model_path: str, vocab_path: str, device: torch.device = None):
        """
        初始化生成器
        
        Args:
            model_path: 模型文件路径
            vocab_path: 词汇表文件路径
            device: 计算设备
        """
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载词汇表
        self.vocab = PoetryVocabulary.load(vocab_path)
        print(f"词汇表加载完成，大小: {self.vocab.vocab_size}")
        
        # 加载模型
        self.model = self._load_model(model_path)
        self.model.eval()
        
        print(f"模型加载完成，设备: {self.device}")
    
    def _load_model(self, model_path: str):
        """加载模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 创建模型
        model = create_model(self.vocab.vocab_size, "lstm")
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        
        return model
    
    def generate(self, start_text: str = "", max_length: int = 100, 
                temperature: float = 1.0, top_k: int = 0, top_p: float = 0.0) -> str:
        """
        生成文本
        
        Args:
            start_text: 起始文本
            max_length: 最大生成长度
            temperature: 温度参数，控制随机性
            top_k: Top-K采样
            top_p: Top-P采样
            
        Returns:
            生成的文本
        """
        with torch.no_grad():
            # 处理起始文本
            if start_text:
                input_indices = self.vocab.text_to_indices(start_text)
                print(f"起始文本: {start_text}")
            else:
                input_indices = [self.vocab.char2idx[self.vocab.START_TOKEN]]
                print("从开始标记生成")
            
            # 初始化隐藏状态
            hidden = self.model.init_hidden(1, self.device)
            
            generated_indices = input_indices.copy()
            
            # 如果有起始文本，先处理起始文本
            if len(input_indices) > 1:
                input_tensor = torch.tensor([input_indices[:-1]], device=self.device)
                _, hidden = self.model(input_tensor, hidden)
                current_input = input_indices[-1]
            else:
                current_input = input_indices[0]
            
            # 生成文本
            for i in range(max_length):
                input_tensor = torch.tensor([[current_input]], device=self.device)
                output, hidden = self.model(input_tensor, hidden)
                
                # 获取logits
                logits = output[0, -1]
                
                # 应用温度
                if temperature != 1.0:
                    logits = logits / temperature
                
                # Top-K采样
                if top_k > 0:
                    top_k_logits, top_k_indices = torch.topk(logits, top_k)
                    logits = torch.full_like(logits, float('-inf'))
                    logits.scatter_(0, top_k_indices, top_k_logits)
                
                # Top-P采样
                if top_p > 0.0:
                    sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                    
                    # 移除累积概率超过top_p的token
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                    sorted_indices_to_remove[0] = 0
                    
                    indices_to_remove = sorted_indices[sorted_indices_to_remove]
                    logits[indices_to_remove] = float('-inf')
                
                # 计算概率分布
                probs = F.softmax(logits, dim=-1)
                
                # 采样下一个字符
                next_char_idx = torch.multinomial(probs, 1).item()
                
                # 检查是否结束
                if next_char_idx == self.vocab.char2idx.get(self.vocab.END_TOKEN, -1):
                    break
                
                generated_indices.append(next_char_idx)
                current_input = next_char_idx
            
            # 转换为文本
            generated_text = self.vocab.indices_to_text(generated_indices)
            return generated_text
    
    def generate_multiple(self, start_text: str = "", num_samples: int = 5, 
                         max_length: int = 100, temperature: float = 1.0) -> List[str]:
        """
        生成多个样本
        
        Args:
            start_text: 起始文本
            num_samples: 生成样本数量
            max_length: 最大生成长度
            temperature: 温度参数
            
        Returns:
            生成的文本列表
        """
        samples = []
        for i in range(num_samples):
            print(f"生成第 {i+1} 个样本...")
            sample = self.generate(start_text, max_length, temperature)
            samples.append(sample)
        return samples
    
    def interactive_generate(self):
        """交互式生成"""
        print("=== 宋词生成器 ===")
        print("输入起始文本，按回车生成。输入 'quit' 退出。")
        print("可选参数: --length <长度> --temp <温度> --topk <top_k> --topp <top_p>")
        
        while True:
            try:
                user_input = input("\n请输入起始文本: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                
                # 解析参数
                parts = user_input.split()
                start_text = ""
                max_length = 100
                temperature = 1.0
                top_k = 0
                top_p = 0.0
                
                i = 0
                while i < len(parts):
                    if parts[i] == '--length' and i + 1 < len(parts):
                        max_length = int(parts[i + 1])
                        i += 2
                    elif parts[i] == '--temp' and i + 1 < len(parts):
                        temperature = float(parts[i + 1])
                        i += 2
                    elif parts[i] == '--topk' and i + 1 < len(parts):
                        top_k = int(parts[i + 1])
                        i += 2
                    elif parts[i] == '--topp' and i + 1 < len(parts):
                        top_p = float(parts[i + 1])
                        i += 2
                    else:
                        start_text += parts[i]
                        i += 1
                
                # 生成文本
                generated = self.generate(
                    start_text=start_text,
                    max_length=max_length,
                    temperature=temperature,
                    top_k=top_k,
                    top_p=top_p
                )
                
                print(f"\n生成的宋词:")
                print(f"{generated}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"生成错误: {e}")
        
        print("再见！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='宋词文本生成器')
    parser.add_argument('--model', type=str, default='./models/best_model.pth',
                       help='模型文件路径')
    parser.add_argument('--vocab', type=str, default='songci_vocab.pkl',
                       help='词汇表文件路径')
    parser.add_argument('--start', type=str, default='',
                       help='起始文本')
    parser.add_argument('--length', type=int, default=100,
                       help='最大生成长度')
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='温度参数')
    parser.add_argument('--top_k', type=int, default=0,
                       help='Top-K采样')
    parser.add_argument('--top_p', type=float, default=0.0,
                       help='Top-P采样')
    parser.add_argument('--num_samples', type=int, default=1,
                       help='生成样本数量')
    parser.add_argument('--interactive', action='store_true',
                       help='交互式生成模式')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.vocab):
        print(f"词汇表文件不存在: {args.vocab}")
        return
    
    # 创建生成器
    generator = SongCiGenerator(args.model, args.vocab)
    
    if args.interactive:
        # 交互式模式
        generator.interactive_generate()
    else:
        # 批量生成模式
        if args.num_samples == 1:
            generated = generator.generate(
                start_text=args.start,
                max_length=args.length,
                temperature=args.temperature,
                top_k=args.top_k,
                top_p=args.top_p
            )
            print(f"生成的宋词:")
            print(generated)
        else:
            samples = generator.generate_multiple(
                start_text=args.start,
                num_samples=args.num_samples,
                max_length=args.length,
                temperature=args.temperature
            )
            for i, sample in enumerate(samples, 1):
                print(f"\n=== 样本 {i} ===")
                print(sample)

if __name__ == "__main__":
    main()
