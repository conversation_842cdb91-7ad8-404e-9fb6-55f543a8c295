#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
古诗数据预处理模块
包含数据读取、词典构建、文本向量化等功能
"""

import pandas as pd
import numpy as np
import re
import pickle
from collections import Counter
from typing import List, Dict, Tuple, Optional
import torch
from torch.utils.data import Dataset, DataLoader

class PoetryVocabulary:
    """诗词词汇表类"""
    
    def __init__(self):
        self.char2idx = {}
        self.idx2char = {}
        self.vocab_size = 0
        
        # 特殊标记
        self.PAD_TOKEN = '<PAD>'
        self.UNK_TOKEN = '<UNK>'
        self.START_TOKEN = '<START>'
        self.END_TOKEN = '<END>'
        
        # 添加特殊标记
        self.special_tokens = [self.PAD_TOKEN, self.UNK_TOKEN, self.START_TOKEN, self.END_TOKEN]
        
    def build_vocab(self, texts: List[str], min_freq: int = 2):
        """构建词汇表"""
        print("构建词汇表...")
        
        # 统计字符频率
        char_counter = Counter()
        for text in texts:
            # 清理文本，只保留中文字符和基本标点
            cleaned_text = re.sub(r'[^\u4e00-\u9fff，。、；：！？]', '', text)
            char_counter.update(cleaned_text)
        
        print(f"总共发现 {len(char_counter)} 个不同字符")
        
        # 构建字符到索引的映射
        self.char2idx = {}
        self.idx2char = {}
        
        # 先添加特殊标记
        for i, token in enumerate(self.special_tokens):
            self.char2idx[token] = i
            self.idx2char[i] = token
        
        # 添加高频字符
        idx = len(self.special_tokens)
        for char, freq in char_counter.most_common():
            if freq >= min_freq:
                self.char2idx[char] = idx
                self.idx2char[idx] = char
                idx += 1
        
        self.vocab_size = len(self.char2idx)
        print(f"词汇表大小: {self.vocab_size}")
        print(f"最高频的10个字符: {char_counter.most_common(10)}")
        
    def text_to_indices(self, text: str) -> List[int]:
        """将文本转换为索引序列"""
        cleaned_text = re.sub(r'[^\u4e00-\u9fff，。、；：！？]', '', text)
        indices = []
        for char in cleaned_text:
            if char in self.char2idx:
                indices.append(self.char2idx[char])
            else:
                indices.append(self.char2idx[self.UNK_TOKEN])
        return indices
    
    def indices_to_text(self, indices: List[int]) -> str:
        """将索引序列转换为文本"""
        chars = []
        for idx in indices:
            if idx in self.idx2char:
                char = self.idx2char[idx]
                if char not in self.special_tokens:
                    chars.append(char)
        return ''.join(chars)
    
    def save(self, filepath: str):
        """保存词汇表"""
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)
    
    @classmethod
    def load(cls, filepath: str):
        """加载词汇表"""
        with open(filepath, 'rb') as f:
            return pickle.load(f)

class PoetryDataset(Dataset):
    """诗词数据集类"""
    
    def __init__(self, texts: List[str], vocab: PoetryVocabulary, seq_length: int = 64):
        self.texts = texts
        self.vocab = vocab
        self.seq_length = seq_length
        self.sequences = self._prepare_sequences()
    
    def _prepare_sequences(self) -> List[Tuple[List[int], List[int]]]:
        """准备训练序列"""
        sequences = []
        
        for text in self.texts:
            # 转换为索引
            indices = self.vocab.text_to_indices(text)
            
            # 添加开始和结束标记
            indices = [self.vocab.char2idx[self.vocab.START_TOKEN]] + indices + [self.vocab.char2idx[self.vocab.END_TOKEN]]
            
            # 创建滑动窗口序列
            for i in range(len(indices) - 1):
                if i + self.seq_length < len(indices):
                    input_seq = indices[i:i + self.seq_length]
                    target_seq = indices[i + 1:i + self.seq_length + 1]
                    sequences.append((input_seq, target_seq))
        
        return sequences
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        input_seq, target_seq = self.sequences[idx]
        return torch.tensor(input_seq, dtype=torch.long), torch.tensor(target_seq, dtype=torch.long)

def load_poetry_data(csv_file: str, sample_size: Optional[int] = None) -> List[str]:
    """加载宋词数据"""
    print(f"从 {csv_file} 加载数据...")

    df = pd.read_csv(csv_file, encoding='utf-8')
    print(f"总共加载了 {len(df)} 首词")

    # 如果指定了样本大小，则随机采样
    if sample_size and sample_size < len(df):
        df = df.sample(n=sample_size, random_state=42)
        print(f"随机采样了 {sample_size} 首词")

    # 提取词文内容
    texts = []
    for _, row in df.iterrows():
        # 宋词数据使用'内容'字段
        text = row['内容'] if '内容' in row else row.get('full_text', '')
        if pd.notna(text) and len(text.strip()) > 0:
            # 清理文本，将|替换为句号
            cleaned_text = text.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            # 移除多余空格
            cleaned_text = re.sub(r'\s+', '', cleaned_text)
            if len(cleaned_text) > 10:  # 过滤太短的词
                texts.append(cleaned_text)

    print(f"有效词文数量: {len(texts)}")
    return texts

def create_data_loader(texts: List[str], vocab: PoetryVocabulary, 
                      seq_length: int = 64, batch_size: int = 32, 
                      shuffle: bool = True) -> DataLoader:
    """创建数据加载器"""
    dataset = PoetryDataset(texts, vocab, seq_length)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)
    print(f"创建数据加载器: {len(dataset)} 个序列, batch_size={batch_size}")
    return dataloader

def preprocess_data(csv_file: str, vocab_file: str, seq_length: int = 64, 
                   batch_size: int = 32, sample_size: Optional[int] = None):
    """完整的数据预处理流程"""
    print("开始数据预处理...")
    
    # 1. 加载数据
    texts = load_poetry_data(csv_file, sample_size)
    
    # 2. 构建词汇表
    vocab = PoetryVocabulary()
    vocab.build_vocab(texts, min_freq=2)
    
    # 3. 保存词汇表
    vocab.save(vocab_file)
    print(f"词汇表已保存到: {vocab_file}")
    
    # 4. 创建数据加载器
    train_loader = create_data_loader(texts, vocab, seq_length, batch_size)
    
    return train_loader, vocab

if __name__ == "__main__":
    # 测试宋词数据预处理
    csv_file = "宋词合集.csv"
    vocab_file = "songci_vocab.pkl"

    train_loader, vocab = preprocess_data(csv_file, vocab_file, sample_size=None)

    # 测试数据加载器
    for batch_idx, (inputs, targets) in enumerate(train_loader):
        print(f"Batch {batch_idx}: inputs shape {inputs.shape}, targets shape {targets.shape}")

        # 显示一个样本
        if batch_idx == 0:
            sample_input = inputs[0]
            sample_target = targets[0]
            input_text = vocab.indices_to_text(sample_input.tolist())
            target_text = vocab.indices_to_text(sample_target.tolist())
            print(f"输入样本: {input_text}")
            print(f"目标样本: {target_text}")

        if batch_idx >= 2:
            break
