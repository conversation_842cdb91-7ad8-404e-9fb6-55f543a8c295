#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宋词生成脚本 - 使用训练好的Transformer模型
"""

import torch
import torch.nn.functional as F
import pickle
from songci_transformer_complete import SongCiTransformer, generate_text

def load_model_and_vocab():
    """加载模型和词汇表"""
    model_path = 'songci_transformer_model.pth'
    vocab_path = 'transformer_char_mappings.pkl'

    print("=== 加载模型和词汇表 ===")

    try:
        # 加载词汇表
        with open(vocab_path, 'rb') as f:
            mappings = pickle.load(f)
            char_to_idx = mappings['char_to_idx']
            idx_to_char = mappings['idx_to_char']

        print(f"✅ 词汇表加载成功，大小: {len(char_to_idx)}")

        # 加载模型
        checkpoint = torch.load(model_path, map_location='cpu')

        model = SongCiTransformer(
            vocab_size=checkpoint['vocab_size'],
            d_model=checkpoint['d_model'],
            nhead=checkpoint['nhead'],
            num_layers=checkpoint['num_layers'],
            dim_feedforward=checkpoint['dim_feedforward'],
            dropout=checkpoint['dropout'],
            max_len=checkpoint['max_len']
        )

        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()

        print("✅ Transformer模型加载成功")
        print(f"模型参数: vocab_size={checkpoint['vocab_size']}, d_model={checkpoint['d_model']}")

        return model, char_to_idx, idx_to_char

    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None, None, None

def interactive_generation():
    """交互式文本生成"""
    print("🚀 宋词Transformer文本生成器")
    print("=" * 50)

    # 加载模型
    model, char_to_idx, idx_to_char = load_model_and_vocab()

    if model is None:
        print("❌ 模型加载失败，请先训练模型")
        return

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    print(f"使用设备: {device}")
    print("\n使用说明:")
    print("- 输入起始文字，程序将生成宋词")
    print("- 可以输入参数调整生成效果")
    print("- 输入格式: 起始文字 [温度] [最大长度]")
    print("- 例如: 春 0.8 60")
    print("- 输入 'quit' 退出程序")
    print("- 输入 'demo' 查看演示")

    while True:
        try:
            user_input = input("\n请输入: ").strip()

            if user_input.lower() == 'quit':
                break

            if user_input.lower() == 'demo':
                demo_generation(model, char_to_idx, idx_to_char, device)
                continue

            if not user_input:
                continue

            # 解析输入
            parts = user_input.split()
            start_text = parts[0]
            temperature = float(parts[1]) if len(parts) > 1 else 1.0
            max_length = int(parts[2]) if len(parts) > 2 else 80

            # 生成文本
            print(f"\n正在生成（起始: '{start_text}', 温度: {temperature}, 长度: {max_length}）...")

            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text,
                max_length=max_length,
                temperature=temperature
            )

            print(f"\n生成结果:")
            print(f"{'='*20}")
            print(generated)
            print(f"{'='*20}")
            print(f"字数: {len(generated)}")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"生成错误: {e}")

    print("感谢使用宋词生成器！")

def demo_generation(model, char_to_idx, idx_to_char, device):
    """演示生成"""
    print("\n=== 演示生成 ===")

    demo_cases = [
        ("春", 0.8, "春天主题"),
        ("花", 1.0, "花朵主题"),
        ("月", 0.6, "月亮主题"),
        ("风", 1.2, "风的主题"),
        ("雨", 0.9, "雨的主题"),
        ("山", 0.7, "山水主题"),
        ("水", 1.1, "水的主题"),
        ("情", 0.8, "情感主题")
    ]

    for start_text, temperature, description in demo_cases:
        print(f"\n{description} ('{start_text}', 温度: {temperature}):")
        print("-" * 30)

        generated = generate_text(
            model, char_to_idx, idx_to_char, device,
            start_text=start_text,
            max_length=60,
            temperature=temperature
        )

        print(generated)

import torch
import torch.nn.functional as F
import pickle
import argparse
from songci_lstm import SongCiLSTM

def load_model_and_vocab(model_path='songci_lstm_model.pth', vocab_path='char_mappings.pkl'):
    """加载模型和词汇表"""
    # 加载词汇表
    with open(vocab_path, 'rb') as f:
        mappings = pickle.load(f)
        char_to_idx = mappings['char_to_idx']
        idx_to_char = mappings['idx_to_char']
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location='cpu')
    
    model = SongCiLSTM(
        vocab_size=checkpoint['vocab_size'],
        embedding_dim=checkpoint['embedding_dim'],
        hidden_dim=checkpoint['hidden_dim'],
        num_layers=checkpoint['num_layers'],
        dropout=checkpoint['dropout']
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, char_to_idx, idx_to_char

def generate_text(model, char_to_idx, idx_to_char, device, start_text="", 
                 max_length=100, temperature=1.0, top_k=0, top_p=0.0):
    """生成文本"""
    model.eval()
    
    with torch.no_grad():
        # 处理起始文本
        if start_text:
            input_chars = list(start_text)
        else:
            # 随机选择一个常见字符作为起始
            common_chars = ['春', '花', '月', '风', '雨', '云', '山', '水']
            import random
            input_chars = [random.choice(common_chars)]
        
        # 转换为索引
        input_indices = [char_to_idx.get(char, 0) for char in input_chars]
        
        # 初始化隐藏状态
        hidden = model.init_hidden(1, device)
        
        generated_chars = input_chars.copy()
        
        # 如果有起始文本，先处理起始文本
        if len(input_indices) > 1:
            input_tensor = torch.tensor([input_indices[:-1]], device=device)
            _, hidden = model(input_tensor, hidden)
            current_input = input_indices[-1]
        else:
            current_input = input_indices[0]
        
        # 生成文本
        for _ in range(max_length):
            input_tensor = torch.tensor([[current_input]], device=device)
            output, hidden = model(input_tensor, hidden)
            
            # 获取logits
            logits = output[0, -1]
            
            # 应用温度
            if temperature != 1.0:
                logits = logits / temperature
            
            # Top-K采样
            if top_k > 0:
                top_k_logits, top_k_indices = torch.topk(logits, top_k)
                logits = torch.full_like(logits, float('-inf'))
                logits.scatter_(0, top_k_indices, top_k_logits)
            
            # Top-P采样
            if top_p > 0.0:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                
                # 移除累积概率超过top_p的token
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                sorted_indices_to_remove[0] = 0
                
                indices_to_remove = sorted_indices[sorted_indices_to_remove]
                logits[indices_to_remove] = float('-inf')
            
            # 计算概率分布
            probs = F.softmax(logits, dim=-1)
            
            # 采样下一个字符
            next_char_idx = torch.multinomial(probs, 1).item()
            next_char = idx_to_char.get(next_char_idx, '')
            
            generated_chars.append(next_char)
            current_input = next_char_idx
            
            # 如果生成了句号，可能结束
            if next_char == '。' and len(generated_chars) > 20:
                break
        
        return ''.join(generated_chars)

def interactive_generate(model, char_to_idx, idx_to_char, device):
    """交互式生成"""
    print("=== 宋词生成器 ===")
    print("输入起始文字，按回车生成宋词。输入 'quit' 退出。")
    print("可选参数格式: 起始文字 --temp 温度值 --length 长度 --topk K值 --topp P值")
    print("例如: 春 --temp 0.8 --length 50")
    
    while True:
        try:
            user_input = input("\n请输入: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            # 解析参数
            parts = user_input.split()
            if not parts:
                continue
            
            start_text = parts[0]
            max_length = 80
            temperature = 1.0
            top_k = 0
            top_p = 0.0
            
            # 解析可选参数
            i = 1
            while i < len(parts):
                if parts[i] == '--temp' and i + 1 < len(parts):
                    temperature = float(parts[i + 1])
                    i += 2
                elif parts[i] == '--length' and i + 1 < len(parts):
                    max_length = int(parts[i + 1])
                    i += 2
                elif parts[i] == '--topk' and i + 1 < len(parts):
                    top_k = int(parts[i + 1])
                    i += 2
                elif parts[i] == '--topp' and i + 1 < len(parts):
                    top_p = float(parts[i + 1])
                    i += 2
                else:
                    i += 1
            
            # 生成文本
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text,
                max_length=max_length,
                temperature=temperature,
                top_k=top_k,
                top_p=top_p
            )
            
            print(f"\n生成的宋词:")
            print(f"{'='*50}")
            print(generated)
            print(f"{'='*50}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"生成错误: {e}")
    
    print("再见！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='宋词文本生成器')
    parser.add_argument('--model', type=str, default='songci_lstm_model.pth',
                       help='模型文件路径')
    parser.add_argument('--vocab', type=str, default='char_mappings.pkl',
                       help='词汇表文件路径')
    parser.add_argument('--start', type=str, default='',
                       help='起始文本')
    parser.add_argument('--length', type=int, default=80,
                       help='最大生成长度')
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='温度参数 (0.1-2.0)')
    parser.add_argument('--top_k', type=int, default=0,
                       help='Top-K采样')
    parser.add_argument('--top_p', type=float, default=0.0,
                       help='Top-P采样')
    parser.add_argument('--num_samples', type=int, default=1,
                       help='生成样本数量')
    parser.add_argument('--interactive', action='store_true',
                       help='交互式生成模式')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    try:
        model, char_to_idx, idx_to_char = load_model_and_vocab(args.model, args.vocab)
        print("模型和词汇表加载成功！")
    except FileNotFoundError as e:
        print(f"文件不存在: {e}")
        print("请先运行 python songci_lstm.py 训练模型")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    if args.interactive:
        # 交互式模式
        interactive_generate(model, char_to_idx, idx_to_char, device)
    else:
        # 批量生成模式
        print(f"生成参数:")
        print(f"- 起始文本: '{args.start}'")
        print(f"- 最大长度: {args.length}")
        print(f"- 温度: {args.temperature}")
        print(f"- Top-K: {args.top_k}")
        print(f"- Top-P: {args.top_p}")
        print(f"- 样本数量: {args.num_samples}")
        
        for i in range(args.num_samples):
            if args.num_samples > 1:
                print(f"\n=== 样本 {i+1} ===")
            
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=args.start,
                max_length=args.length,
                temperature=args.temperature,
                top_k=args.top_k,
                top_p=args.top_p
            )
            
            print(f"生成的宋词:")
            print(f"{'='*50}")
            print(generated)
            print(f"{'='*50}")

if __name__ == "__main__":
    main()
