#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宋词LSTM模型训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import time
import os
import pickle
from typing import Tuple, Optional

from data_preprocessing import preprocess_data, PoetryVocabulary
from lstm_model import PoetryLSTM, create_model, count_parameters

class Trainer:
    """模型训练器"""
    
    def __init__(self, model: nn.Module, vocab: PoetryVocabulary, 
                 learning_rate: float = 0.001, device: torch.device = None):
        """
        初始化训练器
        
        Args:
            model: LSTM模型
            vocab: 词汇表
            learning_rate: 学习率
            device: 训练设备
        """
        self.model = model
        self.vocab = vocab
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 将模型移到指定设备
        self.model.to(self.device)
        
        # 优化器
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss(ignore_index=vocab.char2idx.get(vocab.PAD_TOKEN, 0))
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        
    def train_epoch(self, train_loader: DataLoader, epoch: int) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = len(train_loader)
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            # 移动数据到设备
            inputs = inputs.to(self.device)
            targets = targets.to(self.device)
            
            # 初始化隐藏状态
            hidden = self.model.init_hidden(inputs.size(0), self.device)
            
            # 前向传播
            outputs, hidden = self.model(inputs, hidden)
            
            # 计算损失
            loss = self.criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=5.0)
            
            # 更新参数
            self.optimizer.step()
            
            total_loss += loss.item()
            
            # 打印进度
            if batch_idx % 100 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}')
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate(self, val_loader: DataLoader) -> float:
        """验证模型"""
        self.model.eval()
        total_loss = 0
        num_batches = len(val_loader)
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                hidden = self.model.init_hidden(inputs.size(0), self.device)
                outputs, hidden = self.model(inputs, hidden)
                
                loss = self.criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
                total_loss += loss.item()
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None,
              num_epochs: int = 50, save_dir: str = './models', save_every: int = 5):
        """训练模型"""
        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {count_parameters(self.model)[0]:,}")
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        best_val_loss = float('inf')
        
        for epoch in range(1, num_epochs + 1):
            start_time = time.time()
            
            # 训练
            train_loss = self.train_epoch(train_loader, epoch)
            self.train_losses.append(train_loss)
            
            # 验证
            if val_loader is not None:
                val_loss = self.validate(val_loader)
                self.val_losses.append(val_loss)
                
                # 更新学习率
                self.scheduler.step(val_loss)
                
                # 保存最佳模型
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    self.save_model(os.path.join(save_dir, 'best_model.pth'))
                
                print(f'Epoch {epoch}/{num_epochs}, Train Loss: {train_loss:.4f}, '
                      f'Val Loss: {val_loss:.4f}, Time: {time.time() - start_time:.2f}s')
            else:
                print(f'Epoch {epoch}/{num_epochs}, Train Loss: {train_loss:.4f}, '
                      f'Time: {time.time() - start_time:.2f}s')
            
            # 定期保存模型
            if epoch % save_every == 0:
                self.save_model(os.path.join(save_dir, f'model_epoch_{epoch}.pth'))
            
            # 生成样本文本
            if epoch % 10 == 0:
                self.generate_sample()
        
        # 保存最终模型
        self.save_model(os.path.join(save_dir, 'final_model.pth'))
        
        # 保存训练历史
        history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        with open(os.path.join(save_dir, 'training_history.pkl'), 'wb') as f:
            pickle.dump(history, f)
    
    def save_model(self, filepath: str):
        """保存模型"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'vocab_size': self.vocab.vocab_size,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        torch.save(checkpoint, filepath)
        print(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])
        print(f"模型已从 {filepath} 加载")
    
    def generate_sample(self, start_text: str = "", max_length: int = 100, temperature: float = 1.0):
        """生成样本文本"""
        self.model.eval()
        
        with torch.no_grad():
            # 处理起始文本
            if start_text:
                input_indices = self.vocab.text_to_indices(start_text)
            else:
                input_indices = [self.vocab.char2idx[self.vocab.START_TOKEN]]
            
            # 初始化隐藏状态
            hidden = self.model.init_hidden(1, self.device)
            
            generated_indices = input_indices.copy()
            
            # 如果有起始文本，先处理起始文本
            if len(input_indices) > 1:
                input_tensor = torch.tensor([input_indices[:-1]], device=self.device)
                _, hidden = self.model(input_tensor, hidden)
                current_input = input_indices[-1]
            else:
                current_input = input_indices[0]
            
            # 生成文本
            for _ in range(max_length):
                input_tensor = torch.tensor([[current_input]], device=self.device)
                output, hidden = self.model(input_tensor, hidden)
                
                # 应用温度
                logits = output[0, -1] / temperature
                probs = torch.softmax(logits, dim=-1)
                
                # 采样下一个字符
                next_char_idx = torch.multinomial(probs, 1).item()
                
                # 检查是否结束
                if next_char_idx == self.vocab.char2idx.get(self.vocab.END_TOKEN, -1):
                    break
                
                generated_indices.append(next_char_idx)
                current_input = next_char_idx
            
            # 转换为文本
            generated_text = self.vocab.indices_to_text(generated_indices)
            print(f"生成的文本: {generated_text}")
            return generated_text

def split_data(train_loader: DataLoader, val_ratio: float = 0.1) -> Tuple[DataLoader, DataLoader]:
    """分割训练和验证数据"""
    dataset = train_loader.dataset
    total_size = len(dataset)
    val_size = int(total_size * val_ratio)
    train_size = total_size - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    train_loader = DataLoader(train_dataset, batch_size=train_loader.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=train_loader.batch_size, shuffle=False)
    
    return train_loader, val_loader

def main():
    """主训练函数"""
    # 配置参数
    csv_file = "宋词合集.csv"
    vocab_file = "songci_vocab.pkl"
    model_dir = "./models"
    
    # 模型参数
    embedding_dim = 256
    hidden_dim = 512
    num_layers = 2
    dropout = 0.3
    learning_rate = 0.001
    
    # 训练参数
    batch_size = 32
    seq_length = 64
    num_epochs = 50
    
    print("开始数据预处理...")
    
    # 数据预处理
    train_loader, vocab = preprocess_data(
        csv_file, vocab_file, seq_length=seq_length, 
        batch_size=batch_size, sample_size=None
    )
    
    # 分割训练和验证数据
    train_loader, val_loader = split_data(train_loader, val_ratio=0.1)
    
    print(f"训练数据: {len(train_loader.dataset)} 个序列")
    print(f"验证数据: {len(val_loader.dataset)} 个序列")
    print(f"词汇表大小: {vocab.vocab_size}")
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = create_model(
        vocab.vocab_size, "lstm",
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout=dropout
    )
    
    # 创建训练器
    trainer = Trainer(model, vocab, learning_rate=learning_rate, device=device)
    
    # 开始训练
    trainer.train(
        train_loader, val_loader,
        num_epochs=num_epochs,
        save_dir=model_dir,
        save_every=5
    )
    
    print("训练完成！")

if __name__ == "__main__":
    main()
