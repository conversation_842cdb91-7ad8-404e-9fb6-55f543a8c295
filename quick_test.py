#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证LSTM文本生成系统的各个组件
"""

import torch
import numpy as np
import os
import sys

def test_data_preprocessing():
    """测试数据预处理"""
    print("=== 测试数据预处理 ===")
    
    try:
        from data_preprocessing import preprocess_data
        
        # 使用小样本测试
        train_loader, vocab = preprocess_data(
            csv_file="宋词合集.csv",
            vocab_file="test_vocab.pkl",
            seq_length=32,
            batch_size=8,
            sample_size=5  # 只用5首词测试
        )
        
        print(f"✓ 数据预处理成功")
        print(f"  - 词汇表大小: {vocab.vocab_size}")
        print(f"  - 训练序列数: {len(train_loader.dataset)}")
        
        # 测试一个batch
        for inputs, targets in train_loader:
            print(f"  - 输入形状: {inputs.shape}")
            print(f"  - 目标形状: {targets.shape}")
            break
        
        return True, vocab
        
    except Exception as e:
        print(f"✗ 数据预处理失败: {e}")
        return False, None

def test_model_creation(vocab):
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        from lstm_model import create_model, count_parameters
        
        # 创建小模型
        model = create_model(
            vocab_size=vocab.vocab_size,
            model_type="lstm",
            embedding_dim=64,
            hidden_dim=128,
            num_layers=1,
            dropout=0.1
        )
        
        total_params, trainable_params = count_parameters(model)
        print(f"✓ 模型创建成功")
        print(f"  - 参数数量: {total_params:,}")
        print(f"  - 可训练参数: {trainable_params:,}")
        
        # 测试前向传播
        device = torch.device('cpu')
        model = model.to(device)
        
        batch_size, seq_len = 2, 16
        x = torch.randint(0, vocab.vocab_size, (batch_size, seq_len))
        hidden = model.init_hidden(batch_size, device)
        
        output, new_hidden = model(x, hidden)
        print(f"  - 前向传播成功: {output.shape}")
        
        return True, model
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False, None

def test_training_step(model, vocab):
    """测试训练步骤"""
    print("\n=== 测试训练步骤 ===")
    
    try:
        import torch.nn as nn
        import torch.optim as optim
        
        # 创建简单的训练数据
        batch_size, seq_len = 2, 16
        device = torch.device('cpu')
        
        inputs = torch.randint(0, vocab.vocab_size, (batch_size, seq_len))
        targets = torch.randint(0, vocab.vocab_size, (batch_size, seq_len))
        
        # 设置优化器和损失函数
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        # 训练一步
        model.train()
        hidden = model.init_hidden(batch_size, device)
        
        optimizer.zero_grad()
        outputs, hidden = model(inputs, hidden)
        loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
        loss.backward()
        optimizer.step()
        
        print(f"✓ 训练步骤成功")
        print(f"  - 损失值: {loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练步骤失败: {e}")
        return False

def test_text_generation(model, vocab):
    """测试文本生成"""
    print("\n=== 测试文本生成 ===")
    
    try:
        model.eval()
        device = torch.device('cpu')
        
        with torch.no_grad():
            # 简单的生成测试
            start_indices = [vocab.char2idx.get(vocab.START_TOKEN, 0)]
            hidden = model.init_hidden(1, device)
            
            generated_indices = start_indices.copy()
            
            # 生成10个字符
            for _ in range(10):
                input_tensor = torch.tensor([[generated_indices[-1]]], device=device)
                output, hidden = model(input_tensor, hidden)
                
                # 简单采样
                probs = torch.softmax(output[0, -1], dim=-1)
                next_idx = torch.multinomial(probs, 1).item()
                
                generated_indices.append(next_idx)
            
            # 转换为文本
            generated_text = vocab.indices_to_text(generated_indices)
            
            print(f"✓ 文本生成成功")
            print(f"  - 生成文本: '{generated_text}'")
            
            return True
            
    except Exception as e:
        print(f"✗ 文本生成失败: {e}")
        return False

def test_save_load_model(model, vocab):
    """测试模型保存和加载"""
    print("\n=== 测试模型保存和加载 ===")
    
    try:
        # 保存模型
        checkpoint = {
            'model_state_dict': model.state_dict(),
            'vocab_size': vocab.vocab_size,
        }
        torch.save(checkpoint, 'test_model.pth')
        
        # 加载模型
        from lstm_model import create_model
        
        checkpoint = torch.load('test_model.pth', map_location='cpu')
        new_model = create_model(
            vocab_size=checkpoint['vocab_size'],
            model_type="lstm",
            embedding_dim=64,
            hidden_dim=128,
            num_layers=1,
            dropout=0.1
        )
        new_model.load_state_dict(checkpoint['model_state_dict'])
        
        print(f"✓ 模型保存和加载成功")
        
        # 清理测试文件
        if os.path.exists('test_model.pth'):
            os.remove('test_model.pth')
        if os.path.exists('test_vocab.pkl'):
            os.remove('test_vocab.pkl')
        
        return True
        
    except Exception as e:
        print(f"✗ 模型保存和加载失败: {e}")
        return False

def main():
    """主测试函数"""
    print("宋词LSTM文本生成系统 - 快速测试")
    print("=" * 50)
    
    # 检查数据文件
    if not os.path.exists("宋词合集.csv"):
        print("✗ 错误: 找不到宋词合集.csv文件")
        return
    
    success_count = 0
    total_tests = 5
    
    # 1. 测试数据预处理
    success, vocab = test_data_preprocessing()
    if success:
        success_count += 1
    else:
        print("数据预处理失败，停止测试")
        return
    
    # 2. 测试模型创建
    success, model = test_model_creation(vocab)
    if success:
        success_count += 1
    else:
        print("模型创建失败，停止测试")
        return
    
    # 3. 测试训练步骤
    if test_training_step(model, vocab):
        success_count += 1
    
    # 4. 测试文本生成
    if test_text_generation(model, vocab):
        success_count += 1
    
    # 5. 测试模型保存和加载
    if test_save_load_model(model, vocab):
        success_count += 1
    
    # 总结
    print("\n" + "=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 项测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！系统运行正常。")
        print("\n接下来可以运行:")
        print("  python main.py demo --sample_size 50 --epochs 3")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
