# 宋词LSTM文本生成系统

基于LSTM神经网络的宋词文本生成系统，能够学习宋词的语言模式并生成新的词作。

## 功能特点

- **数据预处理**: 自动处理宋词CSV数据，构建字符级词汇表
- **LSTM模型**: 实现了多层LSTM网络，支持嵌入层、隐藏层和输出层
- **训练循环**: 完整的训练流程，包括损失计算、反向传播和优化
- **文本生成**: 支持多种生成策略，包括温度控制、Top-K和Top-P采样
- **交互式界面**: 提供命令行交互式生成模式

## 文件结构

```
├── 宋词合集.csv              # 宋词数据文件
├── main.py                   # 主程序入口
├── data_preprocessing.py     # 数据预处理模块
├── lstm_model.py            # LSTM模型定义
├── train_model.py           # 模型训练脚本
├── text_generator.py        # 文本生成器
└── LSTM_README.md          # 本说明文档
```

## 安装依赖

```bash
pip install torch pandas numpy
```

## 使用方法

### 1. 快速演示

运行完整的演示流程（使用小样本数据）：

```bash
python main.py demo
```

这将执行：
- 数据预处理（使用100首词样本）
- 模型训练（5个epoch）
- 文本生成（生成3个样本）

### 2. 分步执行

#### 步骤1: 数据预处理

```bash
python main.py preprocess --csv_file 宋词合集.csv --vocab_file songci_vocab.pkl
```

参数说明：
- `--csv_file`: 宋词CSV文件路径
- `--vocab_file`: 词汇表保存路径
- `--seq_length`: 序列长度（默认64）
- `--batch_size`: 批大小（默认32）
- `--sample_size`: 采样大小（可选，用于测试）

#### 步骤2: 模型训练

```bash
python main.py train --epochs 50 --batch_size 32
```

参数说明：
- `--epochs`: 训练轮数（默认50）
- `--batch_size`: 批大小（默认32）
- `--model_dir`: 模型保存目录（默认./models）
- `--seq_length`: 序列长度（默认64）

#### 步骤3: 文本生成

```bash
# 单次生成
python main.py generate --start_text "春" --max_length 100 --temperature 1.0

# 生成多个样本
python main.py generate --start_text "春" --num_samples 5 --temperature 0.8

# 交互式生成
python main.py generate --interactive
```

参数说明：
- `--start_text`: 起始文本
- `--max_length`: 最大生成长度
- `--temperature`: 温度参数（控制随机性，0.1-2.0）
- `--num_samples`: 生成样本数量
- `--interactive`: 交互式模式

### 3. 直接运行脚本

也可以直接运行各个模块：

```bash
# 数据预处理
python data_preprocessing.py

# 模型训练
python train_model.py

# 文本生成
python text_generator.py --model ./models/best_model.pth --start "春风" --interactive
```

## 模型架构

### LSTM网络结构

```
输入 -> 嵌入层 -> LSTM层(多层) -> Dropout -> 输出层 -> Softmax
```

### 默认参数

- **嵌入维度**: 256
- **隐藏层维度**: 512
- **LSTM层数**: 2
- **Dropout率**: 0.3
- **学习率**: 0.001
- **序列长度**: 64
- **批大小**: 32

## 生成策略

### 1. 温度采样
- `temperature = 0.1`: 更确定性的输出
- `temperature = 1.0`: 平衡的随机性
- `temperature = 2.0`: 更随机的输出

### 2. Top-K采样
只从概率最高的K个候选中采样

### 3. Top-P采样
从累积概率达到P的候选中采样

## 示例输出

```
起始文本: 春
生成的宋词:
春风又绿江南岸，明月何时照我还。
花开花落无人问，燕去燕来似有情。
```

## 训练监控

训练过程中会显示：
- 每个epoch的训练损失和验证损失
- 学习率调整信息
- 定期生成的样本文本
- 模型保存信息

## 文件说明

### 数据文件
- `宋词合集.csv`: 包含词牌名、作者、内容、朝代、来源等字段

### 模型文件
- `songci_vocab.pkl`: 字符级词汇表
- `./models/best_model.pth`: 最佳模型权重
- `./models/final_model.pth`: 最终模型权重
- `./models/training_history.pkl`: 训练历史记录

## 注意事项

1. **内存需求**: 完整数据集训练需要较大内存，建议使用GPU
2. **训练时间**: 完整训练可能需要数小时，建议先用小样本测试
3. **生成质量**: 训练轮数越多，生成质量通常越好
4. **参数调优**: 可以调整温度、序列长度等参数来改善生成效果

## 故障排除

### 常见问题

1. **内存不足**: 减小batch_size或seq_length
2. **CUDA错误**: 确保PyTorch版本与CUDA版本兼容
3. **生成质量差**: 增加训练轮数或调整模型参数
4. **文件不存在**: 确保按顺序执行预处理、训练、生成步骤

### 调试模式

使用小样本进行快速测试：
```bash
python main.py preprocess --sample_size 50
python main.py train --epochs 3
```

## 扩展功能

- 支持不同的模型架构（基础LSTM、带注意力的LSTM）
- 可以调整网络层数和隐藏层大小
- 支持自定义词汇表构建策略
- 可以添加更多的文本生成策略

## 技术细节

### 数据预处理
1. 加载CSV文件中的宋词数据
2. 清理文本，将"|"分隔符替换为句号
3. 构建字符级词汇表，包含特殊标记
4. 将文本转换为训练序列

### 模型训练
1. 使用Adam优化器
2. 交叉熵损失函数
3. 梯度裁剪防止梯度爆炸
4. 学习率调度器自动调整学习率
5. 早停机制防止过拟合

### 文本生成
1. 支持种子文本输入
2. 温度控制生成随机性
3. Top-K和Top-P采样策略
4. 自动停止生成（遇到结束标记）

## 许可证

本项目基于原始chinese-poetry数据集，仅供学习和研究使用。
