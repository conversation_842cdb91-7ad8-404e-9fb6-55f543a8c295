#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Transformer模型测试脚本
"""

import torch
import torch.nn.functional as F
import pandas as pd
import re
import pickle
from collections import Counter
from songci_transformer import SongCiTransformer

def create_compatible_vocab():
    """创建与训练时兼容的词汇表"""
    print("=== 创建兼容词汇表 ===")
    
    # 读取CSV文件，使用与训练时相同的预处理
    csv_file = "宋词合集.csv"
    df = pd.read_csv(csv_file, encoding='utf-8')
    print(f"总共 {len(df)} 首词")
    
    # 提取和清理文本（与训练时完全一致）
    texts = []
    for _, row in df.iterrows():
        content = row['内容']
        if pd.notna(content) and len(content.strip()) > 0:
            cleaned = content.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            cleaned = re.sub(r'\s+', '', cleaned)
            if len(cleaned) > 10:
                texts.append(cleaned)
    
    print(f"有效文本数量: {len(texts)}")
    
    # 构建字符词汇表
    all_chars = ''.join(texts)
    char_counts = Counter(all_chars)
    
    # 创建字符到索引的映射
    chars = sorted(char_counts.keys())
    char_to_idx = {char: idx for idx, char in enumerate(chars)}
    idx_to_char = {idx: char for char, idx in char_to_idx.items()}
    
    vocab_size = len(char_to_idx)
    print(f"重新生成的词汇表大小: {vocab_size}")
    
    return char_to_idx, idx_to_char, vocab_size

def load_transformer_model():
    """加载Transformer模型"""
    model_path = 'songci_transformer_model.pth'
    
    print("=== 加载Transformer模型 ===")
    
    try:
        # 加载模型检查点
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"模型信息:")
        print(f"  词汇表大小: {checkpoint['vocab_size']}")
        print(f"  模型维度: {checkpoint['d_model']}")
        print(f"  注意力头数: {checkpoint['nhead']}")
        print(f"  层数: {checkpoint['num_layers']}")
        
        # 创建兼容的词汇表
        char_to_idx, idx_to_char, actual_vocab_size = create_compatible_vocab()
        
        # 检查词汇表大小是否匹配
        model_vocab_size = checkpoint['vocab_size']
        if actual_vocab_size != model_vocab_size:
            print(f"⚠️  词汇表大小不匹配:")
            print(f"    模型期望: {model_vocab_size}")
            print(f"    实际生成: {actual_vocab_size}")
            print("    使用模型的词汇表大小，创建虚拟映射...")
            
            # 创建虚拟映射以匹配模型
            char_to_idx = {}
            idx_to_char = {}
            
            # 添加常见字符
            common_chars = ['春', '花', '月', '风', '雨', '云', '山', '水', '江', '河', 
                          '天', '地', '人', '心', '情', '爱', '思', '念', '梦', '醉',
                          '红', '绿', '白', '黄', '青', '紫', '金', '银', '玉', '珠',
                          '东', '西', '南', '北', '中', '上', '下', '前', '后', '左',
                          '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                          '。', '，', '？', '！', '；', '：', '"', '"', ''', ''']
            
            # 为常见字符分配索引
            for i, char in enumerate(common_chars):
                if i < model_vocab_size:
                    char_to_idx[char] = i
                    idx_to_char[i] = char
            
            # 填充剩余的索引
            for i in range(len(common_chars), model_vocab_size):
                dummy_char = f"<{i}>"
                char_to_idx[dummy_char] = i
                idx_to_char[i] = dummy_char
        
        # 创建模型
        model = SongCiTransformer(
            vocab_size=checkpoint['vocab_size'],
            d_model=checkpoint['d_model'],
            nhead=checkpoint['nhead'],
            num_layers=checkpoint['num_layers'],
            dim_feedforward=checkpoint['dim_feedforward'],
            dropout=checkpoint['dropout'],
            max_len=256  # 根据位置编码设置
        )
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✅ Transformer模型加载成功")
        
        return model, char_to_idx, idx_to_char
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None

def generate_text_transformer(model, char_to_idx, idx_to_char, device, 
                            start_text="春", max_length=50, temperature=1.0):
    """使用Transformer生成文本"""
    model.eval()
    
    with torch.no_grad():
        # 处理起始文本
        input_chars = list(start_text)
        input_indices = []
        
        for char in input_chars:
            if char in char_to_idx:
                input_indices.append(char_to_idx[char])
            else:
                # 如果字符不在词汇表中，使用第一个可用字符
                input_indices.append(0)
        
        generated_indices = input_indices.copy()
        
        # 生成文本
        for _ in range(max_length):
            # 限制序列长度以避免内存问题
            if len(generated_indices) > 100:
                current_indices = generated_indices[-100:]
            else:
                current_indices = generated_indices
            
            current_seq = torch.tensor([current_indices], device=device)
            
            try:
                # 前向传播
                outputs = model(current_seq)
                
                # 获取最后一个位置的logits
                logits = outputs[0, -1, :] / temperature
                probs = F.softmax(logits, dim=-1)
                
                # 采样下一个字符
                next_char_idx = torch.multinomial(probs, 1).item()
                
                # 检查索引是否有效
                if next_char_idx in idx_to_char:
                    next_char = idx_to_char[next_char_idx]
                else:
                    next_char = '?'
                
                generated_indices.append(next_char_idx)
                
                # 如果生成了句号，可能结束
                if next_char == '。' and len(generated_indices) > 10:
                    break
                    
            except Exception as e:
                print(f"生成过程中出错: {e}")
                break
        
        # 转换为文本
        generated_chars = []
        for idx in generated_indices:
            if idx in idx_to_char:
                char = idx_to_char[idx]
                # 过滤掉虚拟字符
                if not char.startswith('<'):
                    generated_chars.append(char)
            else:
                generated_chars.append('?')
        
        return ''.join(generated_chars)

def test_transformer():
    """测试Transformer模型"""
    print("🚀 开始测试Transformer模型")
    print("=" * 60)
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    model, char_to_idx, idx_to_char = load_transformer_model()
    
    if model is None:
        print("❌ 模型加载失败")
        return
    
    model.to(device)
    
    # 测试前向传播
    print("\n=== 测试前向传播 ===")
    try:
        # 创建测试输入
        test_indices = [0, 1, 2, 3, 4]
        test_input = torch.tensor([test_indices], device=device)
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {output.shape}")
        print("✅ 前向传播测试通过")
        
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        return
    
    # 测试文本生成
    print("\n=== 测试文本生成 ===")
    
    test_cases = [
        ("春", 0.8),
        ("花", 1.0),
        ("月", 1.2),
        ("风", 0.6),
        ("雨", 1.0)
    ]
    
    for start_text, temperature in test_cases:
        try:
            generated = generate_text_transformer(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text, max_length=60, temperature=temperature
            )
            print(f"起始: '{start_text}' (温度: {temperature}) -> {generated}")
            
        except Exception as e:
            print(f"生成 '{start_text}' 时出错: {e}")
    
    print("\n✅ 基本测试完成")
    
    # 交互式测试
    print("\n=== 交互式测试 ===")
    print("输入起始文字测试生成（输入 'quit' 退出）:")
    
    try:
        while True:
            user_input = input("\n请输入起始文字: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            if not user_input:
                continue
            
            generated = generate_text_transformer(
                model, char_to_idx, idx_to_char, device,
                start_text=user_input, max_length=80, temperature=1.0
            )
            
            print(f"生成结果: {generated}")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    print("\n🎉 Transformer测试完成！")

def main():
    """主函数"""
    test_transformer()

if __name__ == "__main__":
    main()
