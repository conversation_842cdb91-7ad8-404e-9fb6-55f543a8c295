#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Transformer的宋词文本生成系统
包含模型构建、训练循环、生成文本
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
import re
import pickle
import math
import time
import json
from collections import Counter
from tqdm import tqdm

# ==================== 位置编码 ====================

class PositionalEncoding(nn.Module):
    """位置编码层"""
    
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

# ==================== Transformer模型 ====================

class TransformerModel(nn.Module):
    """Transformer语言模型"""
    
    def __init__(self, ntoken, ninp, nhead, nhid, nlayers, dropout=0.5):
        super(TransformerModel, self).__init__()
        
        self.model_type = 'Transformer'
        self.src_mask = None
        self.ninp = ninp
        self.ntoken = ntoken
        
        # 位置编码层
        self.pos_encoder = PositionalEncoding(ninp, dropout)
        
        # Transformer编码器层
        encoder_layers = nn.TransformerEncoderLayer(
            d_model=ninp, 
            nhead=nhead, 
            dim_feedforward=nhid, 
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, nlayers)
        
        # 词嵌入层
        self.encoder = nn.Embedding(ntoken, ninp)
        
        # 输出解码层
        self.decoder = nn.Linear(ninp, ntoken)
        
        self.init_weights()

    def _generate_square_subsequent_mask(self, sz):
        """生成下三角注意力掩码"""
        mask = torch.triu(torch.ones(sz, sz), diagonal=1)
        mask = mask.masked_fill(mask == 1, float('-inf'))
        return mask

    def init_weights(self):
        """初始化权重"""
        initrange = 0.1
        nn.init.uniform_(self.encoder.weight, -initrange, initrange)
        nn.init.uniform_(self.decoder.weight, -initrange, initrange)
        nn.init.zeros_(self.decoder.bias)

    def forward(self, src, has_mask=True):
        """前向传播"""
        if has_mask:
            device = src.device
            if self.src_mask is None or self.src_mask.size(0) != src.size(1):
                mask = self._generate_square_subsequent_mask(src.size(1)).to(device)
                self.src_mask = mask
        else:
            self.src_mask = None

        # 词嵌入并缩放
        src = self.encoder(src) * math.sqrt(self.ninp)
        
        # 位置编码
        src = src.transpose(0, 1)  # (seq_len, batch_size, d_model)
        src = self.pos_encoder(src)
        src = src.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Transformer编码器
        output = self.transformer_encoder(src, mask=self.src_mask)
        
        # 输出层
        output = self.decoder(output)
        
        return F.log_softmax(output, dim=-1)

# ==================== 数据集类 ====================

class SongCiDataset(Dataset):
    """宋词数据集"""
    
    def __init__(self, texts, char_to_idx, seq_length=100):
        self.texts = texts
        self.char_to_idx = char_to_idx
        self.seq_length = seq_length
        self.data = self._prepare_data()
    
    def _prepare_data(self):
        """准备训练数据"""
        data = []
        
        for text in self.texts:
            # 转换为索引序列
            indices = [self.char_to_idx.get(char, 0) for char in text]
            
            # 创建滑动窗口
            for i in range(0, len(indices) - self.seq_length, self.seq_length // 2):
                if i + self.seq_length + 1 <= len(indices):
                    input_seq = indices[i:i + self.seq_length]
                    target_seq = indices[i + 1:i + self.seq_length + 1]
                    data.append((input_seq, target_seq))
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        input_seq, target_seq = self.data[idx]
        return torch.tensor(input_seq, dtype=torch.long), torch.tensor(target_seq, dtype=torch.long)

# ==================== 数据预处理 ====================

def load_and_preprocess_data(csv_file, sample_size=None):
    """加载和预处理数据"""
    print("=== 数据预处理 ===")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file, encoding='utf-8')
    print(f"总共 {len(df)} 首词")
    
    # 提取和清理文本
    texts = []
    for _, row in df.iterrows():
        content = row['内容']
        if pd.notna(content) and len(content.strip()) > 0:
            # 清理文本：将|替换为句号，移除多余空格
            cleaned = content.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            cleaned = re.sub(r'\s+', '', cleaned)
            if len(cleaned) > 10:  # 过滤太短的文本
                texts.append(cleaned)
    
    print(f"有效文本数量: {len(texts)}")
    
    # 如果指定了样本大小，随机采样
    if sample_size and sample_size < len(texts):
        import random
        random.seed(42)
        texts = random.sample(texts, sample_size)
        print(f"采样后文本数量: {len(texts)}")
    
    # 构建字符词汇表
    all_chars = ''.join(texts)
    char_counts = Counter(all_chars)
    
    # 创建字符到索引的映射
    chars = sorted(char_counts.keys())
    char_to_idx = {char: idx for idx, char in enumerate(chars)}
    idx_to_char = {idx: char for char, idx in char_to_idx.items()}
    
    vocab_size = len(char_to_idx)
    print(f"词汇表大小: {vocab_size}")
    print(f"最常见的10个字符: {char_counts.most_common(10)}")
    
    return texts, char_to_idx, idx_to_char, vocab_size

# ==================== 训练函数 ====================

def train_model(model, train_loader, device, char_to_idx, idx_to_char, num_epochs=20, learning_rate=0.001):
    """训练模型"""
    print("=== 开始训练 ===")
    
    model.to(device)
    criterion = nn.NLLLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.5)
    
    train_losses = []
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}')
        
        for input_seq, target_seq in progress_bar:
            input_seq, target_seq = input_seq.to(device), target_seq.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            output = model(input_seq)
            
            # 计算损失
            loss = criterion(output.reshape(-1, output.size(-1)), target_seq.reshape(-1))
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        # 计算平均损失
        avg_loss = total_loss / num_batches
        train_losses.append(avg_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}')
        
        # 更新学习率
        scheduler.step()
        
        # 生成样本查看训练效果
        if (epoch + 1) % 3 == 0:
            model.eval()
            sample = generate_text(model, char_to_idx, idx_to_char, device, "春", max_length=50)
            print(f"生成样本: {sample}")
            model.train()
    
    return train_losses

# ==================== 文本生成 ====================

def generate_text(model, char_to_idx, idx_to_char, device, start_text="春", 
                 max_length=100, temperature=1.0, top_k=0, top_p=0.0):
    """生成文本"""
    model.eval()
    
    with torch.no_grad():
        # 处理起始文本
        input_chars = list(start_text)
        input_indices = [char_to_idx.get(char, 0) for char in input_chars]
        
        generated_indices = input_indices.copy()
        
        # 生成文本
        for _ in range(max_length):
            # 限制序列长度
            if len(generated_indices) > 200:
                current_indices = generated_indices[-200:]
            else:
                current_indices = generated_indices
            
            current_seq = torch.tensor([current_indices], device=device)
            
            # 前向传播
            outputs = model(current_seq, has_mask=False)
            
            # 获取最后一个位置的logits
            logits = outputs[0, -1, :]
            
            # 应用温度
            if temperature != 1.0:
                logits = logits / temperature
            
            # Top-K采样
            if top_k > 0:
                top_k_logits, top_k_indices = torch.topk(logits, min(top_k, logits.size(-1)))
                logits = torch.full_like(logits, float('-inf'))
                logits.scatter_(0, top_k_indices, top_k_logits)
            
            # Top-P采样
            if top_p > 0.0:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                sorted_indices_to_remove[0] = 0
                
                indices_to_remove = sorted_indices[sorted_indices_to_remove]
                logits[indices_to_remove] = float('-inf')
            
            # 计算概率分布
            probs = F.softmax(logits, dim=-1)
            
            # 采样下一个字符
            next_char_idx = torch.multinomial(probs, 1).item()
            next_char = idx_to_char.get(next_char_idx, '')
            
            generated_indices.append(next_char_idx)
            
            # 如果生成了句号，可能结束
            if next_char == '。' and len(generated_indices) > len(input_indices) + 15:
                break
        
        # 转换为文本
        generated_chars = [idx_to_char.get(idx, '') for idx in generated_indices]
        return ''.join(generated_chars)

# ==================== 主函数 ====================

def main():
    """主函数"""
    # 参数设置
    CSV_FILE = "宋词合集.csv"
    SAMPLE_SIZE = 300  # 使用部分数据进行训练
    SEQ_LENGTH = 80    # 序列长度
    BATCH_SIZE = 16    # 批次大小
    NUM_EPOCHS = 20    # 训练轮数
    LEARNING_RATE = 0.001
    
    # Transformer参数
    NINP = 256         # 词向量维度
    NHEAD = 8          # 注意力头数
    NHID = 512         # 前馈网络隐藏层维度
    NLAYERS = 6        # 编码器层数
    DROPOUT = 0.2      # Dropout概率
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载和预处理数据
    texts, char_to_idx, idx_to_char, vocab_size = load_and_preprocess_data(
        CSV_FILE, sample_size=SAMPLE_SIZE
    )
    
    # 保存词汇表
    mappings = {
        'char_to_idx': char_to_idx,
        'idx_to_char': idx_to_char
    }
    with open('transformer_char_mappings.pkl', 'wb') as f:
        pickle.dump(mappings, f)
    print("词汇表已保存到 transformer_char_mappings.pkl")
    
    # 创建数据集和数据加载器
    dataset = SongCiDataset(texts, char_to_idx, seq_length=SEQ_LENGTH)
    train_loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True)
    
    print(f"训练样本数量: {len(dataset)}")
    
    # 创建模型
    model = TransformerModel(
        ntoken=vocab_size,
        ninp=NINP,
        nhead=NHEAD,
        nhid=NHID,
        nlayers=NLAYERS,
        dropout=DROPOUT
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    train_losses = train_model(
        model, train_loader, device, char_to_idx, idx_to_char,
        num_epochs=NUM_EPOCHS, 
        learning_rate=LEARNING_RATE
    )
    
    # 保存模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'ninp': NINP,
        'nhead': NHEAD,
        'nhid': NHID,
        'nlayers': NLAYERS,
        'dropout': DROPOUT,
        'train_losses': train_losses
    }, 'songci_transformer_model.pth')
    
    print("模型已保存到 songci_transformer_model.pth")
    
    # 测试生成
    print("\n=== 测试文本生成 ===")
    
    test_starts = ["春", "花", "月", "风", "雨", "山", "水", "情"]
    
    for start_text in test_starts:
        generated = generate_text(
            model, char_to_idx, idx_to_char, device,
            start_text=start_text, max_length=80, temperature=0.8
        )
        print(f"起始: '{start_text}' -> {generated}")

# ==================== 交互式生成 ====================

def interactive_generation():
    """交互式文本生成"""
    print("🚀 宋词Transformer文本生成器")
    print("=" * 50)

    try:
        # 加载词汇表
        with open('transformer_char_mappings.pkl', 'rb') as f:
            mappings = pickle.load(f)
            char_to_idx = mappings['char_to_idx']
            idx_to_char = mappings['idx_to_char']

        print(f"✅ 词汇表加载成功，大小: {len(char_to_idx)}")

        # 加载模型
        checkpoint = torch.load('songci_transformer_model.pth', map_location='cpu')

        model = TransformerModel(
            ntoken=checkpoint['vocab_size'],
            ninp=checkpoint['ninp'],
            nhead=checkpoint['nhead'],
            nhid=checkpoint['nhid'],
            nlayers=checkpoint['nlayers'],
            dropout=checkpoint['dropout']
        )

        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()

        print("✅ Transformer模型加载成功")

        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)

        print(f"使用设备: {device}")
        print("\n使用说明:")
        print("- 输入起始文字，程序将生成宋词")
        print("- 输入格式: 起始文字 [温度] [最大长度]")
        print("- 例如: 春 0.8 60")
        print("- 输入 'quit' 退出程序")
        print("- 输入 'demo' 查看演示")

        while True:
            try:
                user_input = input("\n请输入: ").strip()

                if user_input.lower() == 'quit':
                    break

                if user_input.lower() == 'demo':
                    demo_cases = [
                        ("春", 0.8, "春天主题"),
                        ("花", 1.0, "花朵主题"),
                        ("月", 0.6, "月亮主题"),
                        ("风", 1.2, "风的主题"),
                        ("雨", 0.9, "雨的主题")
                    ]

                    for start_text, temperature, description in demo_cases:
                        print(f"\n{description} ('{start_text}', 温度: {temperature}):")
                        print("-" * 30)

                        generated = generate_text(
                            model, char_to_idx, idx_to_char, device,
                            start_text=start_text,
                            max_length=60,
                            temperature=temperature
                        )

                        print(generated)
                    continue

                if not user_input:
                    continue

                # 解析输入
                parts = user_input.split()
                start_text = parts[0]
                temperature = float(parts[1]) if len(parts) > 1 else 1.0
                max_length = int(parts[2]) if len(parts) > 2 else 80

                # 生成文本
                print(f"\n正在生成（起始: '{start_text}', 温度: {temperature}, 长度: {max_length}）...")

                generated = generate_text(
                    model, char_to_idx, idx_to_char, device,
                    start_text=start_text,
                    max_length=max_length,
                    temperature=temperature
                )

                print(f"\n生成结果:")
                print(f"{'='*20}")
                print(generated)
                print(f"{'='*20}")
                print(f"字数: {len(generated)}")

            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"生成错误: {e}")

        print("感谢使用宋词生成器！")

    except Exception as e:
        print(f"❌ 加载失败: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'generate':
        interactive_generation()
    else:
        main()
