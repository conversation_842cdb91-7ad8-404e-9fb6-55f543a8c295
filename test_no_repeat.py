#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的LSTM生成函数，验证是否解决了重复字符问题
"""

import torch
import pickle
from songci_lstm import SongCiLSTM, generate_text

def load_lstm_model():
    """加载LSTM模型和词汇表"""
    model_path = 'songci_lstm_model.pth'
    vocab_path = 'char_mappings.pkl'
    
    print("=== 加载LSTM模型 ===")
    
    try:
        # 加载词汇表
        with open(vocab_path, 'rb') as f:
            mappings = pickle.load(f)
            char_to_idx = mappings['char_to_idx']
            idx_to_char = mappings['idx_to_char']
        
        print(f"✅ 词汇表加载成功，大小: {len(char_to_idx)}")
        
        # 加载模型
        checkpoint = torch.load(model_path, map_location='cpu')
        
        model = SongCiLSTM(
            vocab_size=checkpoint['vocab_size'],
            embedding_dim=checkpoint['embedding_dim'],
            hidden_dim=checkpoint['hidden_dim'],
            num_layers=checkpoint['num_layers'],
            dropout=checkpoint['dropout']
        )
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✅ LSTM模型加载成功")
        
        return model, char_to_idx, idx_to_char
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None, None, None

def test_repetition_fix():
    """测试重复字符修复"""
    print("🚀 测试LSTM重复字符修复")
    print("=" * 60)
    
    # 加载模型
    model, char_to_idx, idx_to_char = load_lstm_model()
    
    if model is None:
        print("❌ 模型加载失败")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    print(f"使用设备: {device}")
    
    # 测试不同的生成参数
    test_cases = [
        {
            "name": "原始生成（可能重复）",
            "params": {
                "temperature": 1.0,
                "repetition_penalty": 1.0,
                "max_repeat": 10  # 允许很多重复
            }
        },
        {
            "name": "轻度重复惩罚",
            "params": {
                "temperature": 1.0,
                "repetition_penalty": 1.1,
                "max_repeat": 3
            }
        },
        {
            "name": "中度重复惩罚",
            "params": {
                "temperature": 0.8,
                "repetition_penalty": 1.2,
                "max_repeat": 2
            }
        },
        {
            "name": "强重复惩罚 + Top-K",
            "params": {
                "temperature": 0.8,
                "repetition_penalty": 1.3,
                "max_repeat": 2,
                "top_k": 20
            }
        },
        {
            "name": "Top-P采样",
            "params": {
                "temperature": 0.9,
                "repetition_penalty": 1.2,
                "max_repeat": 2,
                "top_p": 0.9
            }
        }
    ]
    
    start_texts = ["春", "花", "月", "风"]
    
    for case in test_cases:
        print(f"\n=== {case['name']} ===")
        
        for start_text in start_texts:
            try:
                generated = generate_text(
                    model, char_to_idx, idx_to_char, device,
                    start_text=start_text,
                    max_length=60,
                    **case['params']
                )
                
                # 分析重复情况
                repeat_analysis = analyze_repetition(generated)
                
                print(f"起始: '{start_text}' -> {generated}")
                print(f"  重复分析: {repeat_analysis}")
                
            except Exception as e:
                print(f"生成 '{start_text}' 时出错: {e}")
        
        print("-" * 40)

def analyze_repetition(text):
    """分析文本中的重复情况"""
    if len(text) < 2:
        return "文本太短"
    
    # 统计连续重复字符
    max_consecutive = 1
    current_consecutive = 1
    consecutive_chars = []
    
    for i in range(1, len(text)):
        if text[i] == text[i-1]:
            current_consecutive += 1
        else:
            if current_consecutive > 1:
                consecutive_chars.append((text[i-1], current_consecutive))
            max_consecutive = max(max_consecutive, current_consecutive)
            current_consecutive = 1
    
    # 检查最后一个字符
    if current_consecutive > 1:
        consecutive_chars.append((text[-1], current_consecutive))
        max_consecutive = max(max_consecutive, current_consecutive)
    
    # 统计字符频率
    char_freq = {}
    for char in text:
        char_freq[char] = char_freq.get(char, 0) + 1
    
    # 找出最频繁的字符
    most_frequent = max(char_freq.items(), key=lambda x: x[1]) if char_freq else ('', 0)
    
    analysis = f"最长连续重复: {max_consecutive}"
    if consecutive_chars:
        analysis += f", 连续重复: {consecutive_chars[:3]}"  # 只显示前3个
    analysis += f", 最频繁字符: '{most_frequent[0]}'({most_frequent[1]}次)"
    
    return analysis

def interactive_test():
    """交互式测试"""
    print("\n=== 交互式测试 ===")
    print("输入起始文字测试生成（输入 'quit' 退出）")
    print("可选参数格式: 起始文字 --temp 温度 --penalty 惩罚值 --repeat 最大重复")
    print("例如: 春 --temp 0.8 --penalty 1.2 --repeat 2")
    
    model, char_to_idx, idx_to_char = load_lstm_model()
    if model is None:
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    while True:
        try:
            user_input = input("\n请输入: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            # 解析参数
            parts = user_input.split()
            if not parts:
                continue
            
            start_text = parts[0]
            temperature = 1.0
            repetition_penalty = 1.1
            max_repeat = 2
            top_k = 0
            top_p = 0.0
            
            # 解析可选参数
            i = 1
            while i < len(parts):
                if parts[i] == '--temp' and i + 1 < len(parts):
                    temperature = float(parts[i + 1])
                    i += 2
                elif parts[i] == '--penalty' and i + 1 < len(parts):
                    repetition_penalty = float(parts[i + 1])
                    i += 2
                elif parts[i] == '--repeat' and i + 1 < len(parts):
                    max_repeat = int(parts[i + 1])
                    i += 2
                elif parts[i] == '--topk' and i + 1 < len(parts):
                    top_k = int(parts[i + 1])
                    i += 2
                elif parts[i] == '--topp' and i + 1 < len(parts):
                    top_p = float(parts[i + 1])
                    i += 2
                else:
                    i += 1
            
            # 生成文本
            generated = generate_text(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text,
                max_length=80,
                temperature=temperature,
                repetition_penalty=repetition_penalty,
                max_repeat=max_repeat,
                top_k=top_k,
                top_p=top_p
            )
            
            # 分析结果
            repeat_analysis = analyze_repetition(generated)
            
            print(f"\n生成结果: {generated}")
            print(f"重复分析: {repeat_analysis}")
            print(f"参数: temp={temperature}, penalty={repetition_penalty}, max_repeat={max_repeat}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"生成错误: {e}")
    
    print("测试结束！")

def main():
    """主函数"""
    # 运行自动测试
    test_repetition_fix()
    
    # 运行交互式测试
    interactive_test()

if __name__ == "__main__":
    main()
