#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Transformer模型文件的详细信息
"""

import torch
import pickle

def check_model_file():
    """检查模型文件"""
    model_path = 'songci_transformer_model.pth'
    
    print("=== 检查模型文件 ===")
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        print("模型参数:")
        for key, value in checkpoint.items():
            if key != 'model_state_dict':
                print(f"  {key}: {value}")
        
        print("\n模型权重信息:")
        state_dict = checkpoint['model_state_dict']
        
        for name, param in state_dict.items():
            print(f"  {name}: {param.shape}")
            
        return checkpoint
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def check_vocab_file():
    """检查词汇表文件"""
    vocab_path = 'transformer_char_mappings.pkl'
    
    print("\n=== 检查词汇表文件 ===")
    
    try:
        with open(vocab_path, 'rb') as f:
            mappings = pickle.load(f)
        
        char_to_idx = mappings['char_to_idx']
        idx_to_char = mappings['idx_to_char']
        
        print(f"词汇表大小: {len(char_to_idx)}")
        print("示例字符:")
        for i, (char, idx) in enumerate(list(char_to_idx.items())[:10]):
            print(f"  '{char}' -> {idx}")
        
        return len(char_to_idx)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 检查模型和词汇表信息")
    print("=" * 50)
    
    # 检查模型
    checkpoint = check_model_file()
    
    # 检查词汇表
    vocab_size = check_vocab_file()
    
    if checkpoint and vocab_size:
        print(f"\n=== 问题分析 ===")
        model_vocab_size = checkpoint.get('vocab_size', 'Unknown')
        print(f"模型中记录的词汇表大小: {model_vocab_size}")
        print(f"实际词汇表大小: {vocab_size}")
        
        if model_vocab_size != vocab_size:
            print("⚠️  词汇表大小不匹配！")
        
        # 检查位置编码
        state_dict = checkpoint['model_state_dict']
        if 'pos_encoder.pe' in state_dict:
            pe_shape = state_dict['pos_encoder.pe'].shape
            print(f"位置编码形状: {pe_shape}")
            print(f"位置编码最大长度: {pe_shape[0]}")

if __name__ == "__main__":
    main()
