#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Transformer模型测试
直接使用训练时的词汇表重新生成
"""

import torch
import torch.nn.functional as F
import pandas as pd
import re
import pickle
from collections import Counter
from songci_transformer import SongCiTransformer

def recreate_vocab_from_csv():
    """从CSV重新创建与训练时一致的词汇表"""
    print("=== 重新创建词汇表 ===")
    
    # 读取CSV文件
    csv_file = "宋词合集.csv"
    df = pd.read_csv(csv_file, encoding='utf-8')
    print(f"总共 {len(df)} 首词")
    
    # 提取和清理文本（与训练时完全一致）
    texts = []
    for _, row in df.iterrows():
        content = row['内容']
        if pd.notna(content) and len(content.strip()) > 0:
            # 清理文本：将|替换为句号，移除多余空格
            cleaned = content.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            cleaned = re.sub(r'\s+', '', cleaned)
            if len(cleaned) > 10:  # 过滤太短的文本
                texts.append(cleaned)
    
    print(f"有效文本数量: {len(texts)}")
    
    # 构建字符词汇表
    all_chars = ''.join(texts)
    char_counts = Counter(all_chars)
    
    # 创建字符到索引的映射
    chars = sorted(char_counts.keys())
    char_to_idx = {char: idx for idx, char in enumerate(chars)}
    idx_to_char = {idx: char for char, idx in char_to_idx.items()}
    
    vocab_size = len(char_to_idx)
    print(f"重新生成的词汇表大小: {vocab_size}")
    
    return char_to_idx, idx_to_char, vocab_size

def load_model_with_correct_vocab():
    """加载模型并使用正确的词汇表"""
    model_path = 'songci_transformer_model.pth'
    
    print("=== 加载模型 ===")
    
    try:
        # 加载模型检查点
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"模型词汇表大小: {checkpoint['vocab_size']}")
        
        # 重新创建词汇表
        char_to_idx, idx_to_char, actual_vocab_size = recreate_vocab_from_csv()
        
        if actual_vocab_size != checkpoint['vocab_size']:
            print(f"⚠️  词汇表大小不匹配: 模型={checkpoint['vocab_size']}, 实际={actual_vocab_size}")
            print("这可能是因为训练时使用了不同的数据采样或预处理")
            
            # 尝试使用模型的词汇表大小
            print("使用模型中记录的词汇表大小进行测试...")
            
            # 创建一个虚拟的词汇表映射
            model_vocab_size = checkpoint['vocab_size']
            dummy_char_to_idx = {str(i): i for i in range(model_vocab_size)}
            dummy_idx_to_char = {i: str(i) for i in range(model_vocab_size)}
            
            # 添加一些常见字符
            common_chars = ['春', '花', '月', '风', '雨', '云', '山', '水', '。', '，']
            for i, char in enumerate(common_chars):
                if i < model_vocab_size:
                    dummy_char_to_idx[char] = i
                    dummy_idx_to_char[i] = char
            
            char_to_idx = dummy_char_to_idx
            idx_to_char = dummy_idx_to_char
            actual_vocab_size = model_vocab_size
        
        # 创建模型
        model = SongCiTransformer(
            vocab_size=checkpoint['vocab_size'],
            d_model=checkpoint['d_model'],
            nhead=checkpoint['nhead'],
            num_layers=checkpoint['num_layers'],
            dim_feedforward=checkpoint['dim_feedforward'],
            dropout=checkpoint['dropout'],
            max_len=256  # 根据位置编码设置
        )
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✅ 模型加载成功")
        
        return model, char_to_idx, idx_to_char
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None

def generate_text_simple(model, char_to_idx, idx_to_char, device, start_text="春", max_length=50):
    """简化的文本生成"""
    model.eval()
    
    with torch.no_grad():
        # 处理起始文本
        input_chars = list(start_text)
        input_indices = []
        
        for char in input_chars:
            if char in char_to_idx:
                input_indices.append(char_to_idx[char])
            else:
                # 如果字符不在词汇表中，使用第一个字符
                input_indices.append(0)
        
        generated_indices = input_indices.copy()
        
        # 生成文本
        for _ in range(max_length):
            # 限制序列长度
            if len(generated_indices) > 100:
                current_indices = generated_indices[-100:]
            else:
                current_indices = generated_indices
            
            current_seq = torch.tensor([current_indices], device=device)
            
            try:
                # 前向传播
                outputs = model(current_seq)
                
                # 获取最后一个位置的logits
                logits = outputs[0, -1, :]
                probs = F.softmax(logits, dim=-1)
                
                # 采样下一个字符
                next_char_idx = torch.multinomial(probs, 1).item()
                
                # 检查索引是否有效
                if next_char_idx in idx_to_char:
                    next_char = idx_to_char[next_char_idx]
                else:
                    next_char = '?'
                
                generated_indices.append(next_char_idx)
                
                # 如果生成了句号，可能结束
                if next_char == '。' and len(generated_indices) > 10:
                    break
                    
            except Exception as e:
                print(f"生成过程中出错: {e}")
                break
        
        # 转换为文本
        generated_chars = []
        for idx in generated_indices:
            if idx in idx_to_char:
                generated_chars.append(idx_to_char[idx])
            else:
                generated_chars.append('?')
        
        return ''.join(generated_chars)

def test_model_basic():
    """基本模型测试"""
    print("🚀 开始基本Transformer模型测试")
    print("=" * 60)
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    model, char_to_idx, idx_to_char = load_model_with_correct_vocab()
    
    if model is None:
        print("❌ 模型加载失败")
        return
    
    model.to(device)
    
    # 测试前向传播
    print("\n=== 测试前向传播 ===")
    try:
        # 创建测试输入
        test_indices = [0, 1, 2, 3, 4]  # 使用简单的索引
        test_input = torch.tensor([test_indices], device=device)
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {output.shape}")
        print("✅ 前向传播测试通过")
        
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        return
    
    # 测试文本生成
    print("\n=== 测试文本生成 ===")
    
    test_starts = ["春", "花", "月", "0", "1"]  # 包括一些数字作为备选
    
    for start_text in test_starts:
        try:
            generated = generate_text_simple(
                model, char_to_idx, idx_to_char, device,
                start_text=start_text, max_length=30
            )
            print(f"起始: '{start_text}' -> {generated}")
            
        except Exception as e:
            print(f"生成 '{start_text}' 时出错: {e}")
    
    print("\n✅ 基本测试完成")
    
    # 交互式测试
    print("\n=== 交互式测试 ===")
    print("输入起始文字测试生成（输入 'quit' 退出）:")
    
    try:
        while True:
            user_input = input("\n请输入起始文字: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            if not user_input:
                continue
            
            generated = generate_text_simple(
                model, char_to_idx, idx_to_char, device,
                start_text=user_input, max_length=60
            )
            
            print(f"生成结果: {generated}")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_model_basic()
