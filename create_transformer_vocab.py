#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为Transformer模型创建词汇表文件
从宋词合集.csv重新生成词汇表
"""

import pandas as pd
import pickle
import re
from collections import Counter

def create_transformer_vocab():
    """创建Transformer词汇表"""
    print("=== 创建Transformer词汇表 ===")
    
    # 读取CSV文件
    csv_file = "宋词合集.csv"
    print(f"读取数据文件: {csv_file}")
    
    try:
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"总共 {len(df)} 首词")
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return False
    
    # 提取和清理文本
    texts = []
    for _, row in df.iterrows():
        content = row['内容']
        if pd.notna(content) and len(content.strip()) > 0:
            # 清理文本：将|替换为句号，移除多余空格
            cleaned = content.strip().replace('|', '。').replace('\n', '').replace('\r', '')
            cleaned = re.sub(r'\s+', '', cleaned)
            if len(cleaned) > 10:  # 过滤太短的文本
                texts.append(cleaned)
    
    print(f"有效文本数量: {len(texts)}")
    
    # 构建字符词汇表
    all_chars = ''.join(texts)
    char_counts = Counter(all_chars)
    
    # 创建字符到索引的映射
    chars = sorted(char_counts.keys())
    char_to_idx = {char: idx for idx, char in enumerate(chars)}
    idx_to_char = {idx: char for char, idx in char_to_idx.items()}
    
    vocab_size = len(char_to_idx)
    print(f"词汇表大小: {vocab_size}")
    print(f"最常见字符: {char_counts.most_common(10)}")
    
    # 保存词汇表
    vocab_file = 'transformer_char_mappings.pkl'
    try:
        with open(vocab_file, 'wb') as f:
            pickle.dump({
                'char_to_idx': char_to_idx, 
                'idx_to_char': idx_to_char
            }, f)
        print(f"✅ 词汇表已保存: {vocab_file}")
        
        # 验证保存的文件
        with open(vocab_file, 'rb') as f:
            loaded = pickle.load(f)
            if len(loaded['char_to_idx']) == vocab_size:
                print("✅ 词汇表验证成功")
            else:
                print("❌ 词汇表验证失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 保存词汇表失败: {e}")
        return False

def check_existing_vocab():
    """检查现有词汇表文件"""
    print("\n=== 检查现有词汇表文件 ===")
    
    files_to_check = [
        'char_mappings.pkl',
        'songci_vocab.pkl', 
        'transformer_char_mappings.pkl'
    ]
    
    for file_name in files_to_check:
        try:
            with open(file_name, 'rb') as f:
                data = pickle.load(f)
                print(f"✅ {file_name}: {type(data)}")
                
                if isinstance(data, dict):
                    if 'char_to_idx' in data and 'idx_to_char' in data:
                        print(f"   - 词汇表大小: {len(data['char_to_idx'])}")
                        # 显示一些示例字符
                        sample_chars = list(data['char_to_idx'].keys())[:5]
                        print(f"   - 示例字符: {sample_chars}")
                    else:
                        print(f"   - 键: {list(data.keys())}")
                else:
                    print(f"   - 数据类型: {type(data)}")
                    
        except FileNotFoundError:
            print(f"❌ {file_name}: 文件不存在")
        except Exception as e:
            print(f"❌ {file_name}: 读取失败 - {e}")

def copy_existing_vocab():
    """尝试从现有词汇表复制"""
    print("\n=== 尝试复制现有词汇表 ===")
    
    # 尝试从LSTM的词汇表复制
    source_files = ['char_mappings.pkl', 'songci_vocab.pkl']
    
    for source_file in source_files:
        try:
            with open(source_file, 'rb') as f:
                data = pickle.load(f)
                
            # 检查数据格式
            if isinstance(data, dict) and 'char_to_idx' in data and 'idx_to_char' in data:
                # 直接复制
                target_file = 'transformer_char_mappings.pkl'
                with open(target_file, 'wb') as f:
                    pickle.dump(data, f)
                
                print(f"✅ 从 {source_file} 复制到 {target_file}")
                print(f"   词汇表大小: {len(data['char_to_idx'])}")
                return True
                
            elif isinstance(data, tuple) and len(data) == 2:
                # 可能是 (char_to_idx, idx_to_char) 格式
                char_to_idx, idx_to_char = data
                vocab_data = {
                    'char_to_idx': char_to_idx,
                    'idx_to_char': idx_to_char
                }
                
                target_file = 'transformer_char_mappings.pkl'
                with open(target_file, 'wb') as f:
                    pickle.dump(vocab_data, f)
                
                print(f"✅ 从 {source_file} 转换并复制到 {target_file}")
                print(f"   词汇表大小: {len(char_to_idx)}")
                return True
                
        except FileNotFoundError:
            print(f"⚠️  {source_file} 不存在")
            continue
        except Exception as e:
            print(f"❌ 处理 {source_file} 失败: {e}")
            continue
    
    return False

def main():
    """主函数"""
    print("🚀 创建Transformer词汇表")
    print("=" * 50)
    
    # 1. 检查现有文件
    check_existing_vocab()
    
    # 2. 尝试复制现有词汇表
    if copy_existing_vocab():
        print("\n✅ 词汇表创建成功（从现有文件复制）")
        return
    
    # 3. 从CSV重新生成
    print("\n=== 从CSV重新生成词汇表 ===")
    if create_transformer_vocab():
        print("\n✅ 词汇表创建成功（从CSV生成）")
    else:
        print("\n❌ 词汇表创建失败")

if __name__ == "__main__":
    main()
