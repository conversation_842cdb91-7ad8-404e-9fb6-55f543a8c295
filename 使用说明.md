# 宋词LSTM文本生成系统使用说明

基于宋词合集.csv的LSTM文本生成系统，包含完整的模型构建、训练循环和文本生成功能。

## 文件说明

- `songci_lstm.py` - 主训练脚本，包含数据预处理、模型定义和训练
- `generate_songci.py` - 文本生成脚本，用于加载训练好的模型生成宋词
- `宋词合集.csv` - 宋词数据文件
- `使用说明.md` - 本说明文档

## 快速开始

### 1. 安装依赖

```bash
pip install torch pandas numpy
```

### 2. 训练模型

```bash
python songci_lstm.py
```

这将：
- 加载宋词合集.csv数据
- 预处理文本并构建字符词汇表
- 创建LSTM模型
- 训练模型（默认20个epoch）
- 保存训练好的模型和词汇表

### 3. 生成宋词

训练完成后，使用以下命令生成宋词：

```bash
# 基本生成
python generate_songci.py --start "春"

# 生成多个样本
python generate_songci.py --start "花" --num_samples 3

# 调整生成参数
python generate_songci.py --start "月" --temperature 0.8 --length 60

# 交互式生成
python generate_songci.py --interactive
```

## 详细使用方法

### 训练参数调整

在 `songci_lstm.py` 中可以调整以下参数：

```python
# 数据参数
SAMPLE_SIZE = None      # 使用全部数据，设为数字可快速测试
SEQ_LENGTH = 50         # 序列长度
BATCH_SIZE = 32         # 批大小

# 模型参数
EMBEDDING_DIM = 256     # 嵌入维度
HIDDEN_DIM = 512        # 隐藏层维度
NUM_LAYERS = 2          # LSTM层数
DROPOUT = 0.3           # Dropout率

# 训练参数
NUM_EPOCHS = 20         # 训练轮数
LEARNING_RATE = 0.001   # 学习率
```

### 生成参数说明

#### 温度参数 (temperature)
- `0.1-0.5`: 更确定性的输出，文本更规整但可能重复
- `0.8-1.0`: 平衡的随机性，推荐值
- `1.2-2.0`: 更随机的输出，更有创意但可能不连贯

#### Top-K采样 (top_k)
- 只从概率最高的K个候选字符中采样
- 推荐值：5-50

#### Top-P采样 (top_p)
- 从累积概率达到P的候选字符中采样
- 推荐值：0.8-0.95

### 生成示例

```bash
# 基础生成
python generate_songci.py --start "春风"
# 输出: 春风又绿江南岸，明月何时照我还。花开花落无人问...

# 调整温度
python generate_songci.py --start "花" --temperature 0.6
# 输出: 花开花落年年事，春去春来不相关...

# 使用Top-K采样
python generate_songci.py --start "月" --top_k 10 --temperature 0.8
# 输出: 月明千里寄相思，夜深人静独徘徊...

# 交互式生成
python generate_songci.py --interactive
# 进入交互模式，可以连续输入不同的起始文字
```

### 交互式模式使用

在交互式模式中，可以使用以下格式：

```
请输入: 春
请输入: 花 --temp 0.8
请输入: 月 --temp 0.6 --length 50
请输入: 风 --temp 1.0 --topk 20
请输入: quit  # 退出
```

## 模型架构

```
输入文本 -> 字符索引 -> 嵌入层 -> LSTM层(多层) -> Dropout -> 线性层 -> Softmax -> 输出概率
```

### 关键特性

1. **字符级生成**: 基于字符而非词语，能生成更灵活的文本
2. **多层LSTM**: 使用2层LSTM捕获长期依赖关系
3. **Dropout正则化**: 防止过拟合
4. **梯度裁剪**: 防止梯度爆炸
5. **学习率调度**: 自动调整学习率
6. **多种采样策略**: 支持温度、Top-K、Top-P采样

## 训练过程监控

训练过程中会显示：
- 每50个batch的损失值
- 每个epoch的平均损失
- 每5个epoch生成的样本文本
- 学习率调整信息

## 文件输出

训练完成后会生成：
- `songci_lstm_model.pth` - 训练好的模型
- `char_mappings.pkl` - 字符到索引的映射表

## 性能优化建议

1. **GPU加速**: 如果有GPU，会自动使用CUDA加速训练
2. **批大小**: 根据显存大小调整BATCH_SIZE
3. **序列长度**: 较长的序列能学习更长的依赖关系，但需要更多内存
4. **训练轮数**: 观察损失曲线，避免过拟合

## 故障排除

### 常见问题

1. **内存不足**
   - 减小BATCH_SIZE或SEQ_LENGTH
   - 使用SAMPLE_SIZE限制数据量

2. **生成质量差**
   - 增加训练轮数
   - 调整模型参数（增加隐藏层维度）
   - 尝试不同的温度值

3. **训练速度慢**
   - 使用GPU
   - 减小模型大小
   - 使用数据采样

4. **文件不存在错误**
   - 确保宋词合集.csv在当前目录
   - 先运行训练脚本再运行生成脚本

### 调试模式

快速测试可以设置：
```python
SAMPLE_SIZE = 100    # 只使用100首词
NUM_EPOCHS = 5       # 只训练5个epoch
```

## 扩展功能

可以进一步扩展的功能：
- 添加注意力机制
- 支持条件生成（按作者、词牌名生成）
- 实现束搜索(Beam Search)
- 添加文本质量评估
- 支持多GPU训练

## 示例输出

```
起始文字: 春
生成结果: 春风又绿江南岸，明月何时照我还。花开花落无人问，燕去燕来似有情。

起始文字: 花
生成结果: 花开花落年年事，春去春来不相关。独立小桥风满袖，平林新月人归后。

起始文字: 月
生成结果: 月明千里寄相思，夜深人静独徘徊。云散高唐梦一场，水流花谢两无情。
```
